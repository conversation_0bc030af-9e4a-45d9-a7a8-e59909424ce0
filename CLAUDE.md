# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is a demo website for the `qr-code-styling` npm library. It provides an interactive interface to generate and customize QR codes with various styling options including colors, gradients, shapes, and custom images.

## Build and Development Commands
- `npm start` - Start development server with webpack-dev-server (opens browser automatically)
- `npm run build` - Build production version to `docs/` directory

## Architecture
The application uses a single-page architecture with vanilla JavaScript and Webpack:

- **Entry Point**: `src/index.js` - Main application file that initializes the QR code generator
- **Build Output**: `docs/` directory (serves as GitHub Pages deployment target)
- **State Management**: Custom `NodesBinder` class handles form state and UI updates
- **QR Generation**: Uses `qr-code-styling` library for QR code creation and customization

### Key Components
- **NodesBinder** (`src/js/nodes-binder.js`) - Manages form state binding and updates
- **Utilities** (`src/js/tools.js`) - Helper functions for object manipulation and file handling
- **Main App** (`src/index.js`) - Core application logic with extensive gradient and color handling

### State Management Pattern
The app uses a complex state update pattern with helper objects for color/gradient options:
- `dotsOptionsHelper` - Manages dot styling (solid colors vs gradients)
- `cornersSquareOptionsHelper` - Handles corner square styling
- `cornersDotOptionsHelper` - Controls corner dot appearance
- `backgroundOptionsHelper` - Background color/gradient management

Each helper manages the visibility of UI elements and updates the QR code styling in real-time.

## Development Notes
- Uses Webpack 4 with development and production modes
- ES6 modules with Babel transpilation
- CSS extracted to separate file in production
- Assets (images) processed through file-loader
- Source maps enabled for debugging