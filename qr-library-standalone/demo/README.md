# QR Library Standalone - Demo Completa

Esta é uma interface de usuário completa para testar e demonstrar todas as funcionalidades da biblioteca QR Library Standalone.

## 🚀 Como Usar

### Opção 1: Abrir Diretamente
Simplesmente abra o arquivo `index.html` em um navegador moderno que suporte ES6 modules:

```bash
# Navegue até o diretório da demo
cd qr-library-standalone/demo/

# Abra no navegador (exemplo no macOS)
open index.html

# Ou no Linux
xdg-open index.html

# Ou no Windows
start index.html
```

### Opção 2: Servidor Local
Para melhor compatibilidade, especialmente com imagens externas, use um servidor local:

```bash
# Com Python 3
python -m http.server 8000

# Com Node.js (se tiver http-server instalado)
npx http-server

# Com PHP
php -S localhost:8000
```

Depois acesse: `http://localhost:8000/qr-library-standalone/demo/`

## 🎨 Funcionalidades da Interface

### 📋 Exemplos Rápidos
- **Básico**: QR Code simples preto e branco
- **Gradiente**: QR Code com gradiente linear colorido
- **Corporativo**: Estilo profissional com cantos personalizados
- **Moderno**: Design contemporâneo com gradiente radial
- **Colorido**: QR Code vibrante com múltiplas cores
- **Minimalista**: Design limpo e elegante

### ⚙️ Configurações Básicas
- **Dados do QR Code**: Texto, URL ou qualquer conteúdo
- **Tipo de Renderização**: SVG ou Canvas
- **Forma**: Quadrado ou Círculo
- **Dimensões**: Largura e altura personalizáveis (100-800px)
- **Margem**: Espaçamento ao redor do QR Code (0-50px)

### 🔧 Configurações QR
- **Correção de Erro**: L (~7%), M (~15%), Q (~25%), H (~30%)
- **Modo de Codificação**: Automático, Numérico, Alfanumérico, Byte, Kanji
- **Versão QR**: Automática (0) ou manual (1-40)

### ⚫ Estilização de Pontos
- **6 Tipos Disponíveis**:
  - Quadrado (padrão)
  - Círculos
  - Arredondado
  - Extra Arredondado
  - Elegante
  - Elegante Arredondado
- **Cores**: Sólidas ou gradientes (linear/radial)
- **Gradientes**: Múltiplas paradas de cor com rotação personalizada

### ⬜ Cantos Quadrados
- **3 Tipos**: Quadrado, Círculo, Extra Arredondado
- **Cores Independentes**: Diferentes dos pontos principais
- **Gradientes**: Suporte completo a gradientes personalizados

### 🔘 Pontos dos Cantos
- **2 Tipos**: Quadrado, Círculo
- **Personalização Completa**: Cores e gradientes independentes

### 🎨 Configurações de Fundo
- **Cores**: Sólidas ou gradientes
- **Arredondamento**: Bordas arredondadas (0-100%)
- **Gradientes**: Linear e radial com múltiplas cores

### 🖼️ Imagem/Logo Central
- **URL da Imagem**: Suporte a URLs externas e Data URLs
- **Tamanho**: Ajustável de 10% a 80% do QR Code
- **Margem**: Espaçamento ao redor da imagem (0-20px)
- **Ocultação de Fundo**: Ocultar pontos atrás da imagem
- **Cross Origin**: Configurações para imagens externas

## 🎯 Recursos da Interface

### ⚡ Preview em Tempo Real
- Atualização automática do QR Code ao alterar qualquer configuração
- Debounce de 300ms para otimizar performance
- Indicadores visuais de status (carregando, sucesso, erro)

### 📥 Download Integrado
- **PNG**: Formato raster de alta qualidade
- **JPEG**: Formato comprimido para menor tamanho
- **SVG**: Formato vetorial escalável

### 🎨 Sistema de Gradientes Avançado
- **Tipos**: Linear e radial
- **Múltiplas Cores**: Adicionar/remover paradas de cor dinamicamente
- **Rotação**: Controle preciso da direção (apenas gradientes lineares)
- **Preview Visual**: Visualização em tempo real das mudanças

### 📱 Design Responsivo
- **Desktop**: Layout de duas colunas (controles + preview)
- **Mobile**: Layout empilhado para melhor usabilidade
- **Adaptativo**: Ajusta-se automaticamente ao tamanho da tela

### ✅ Validação e Tratamento de Erros
- **Validação de Entrada**: Verificação de dados inválidos
- **Mensagens Claras**: Feedback visual para erros e sucessos
- **Recuperação Automática**: Fallbacks para configurações inválidas

## 🔧 Estrutura Técnica

### Tecnologias Utilizadas
- **HTML5**: Estrutura semântica moderna
- **CSS3**: Estilos avançados com Grid e Flexbox
- **JavaScript ES6+**: Módulos, async/await, arrow functions
- **QR Library Standalone**: Biblioteca principal sem dependências

### Arquitetura do Código
```javascript
// Principais funções
- updateQRCode()           // Gera/atualiza o QR Code
- collectOptions()         // Coleta todas as configurações
- createGradient()         // Cria objetos de gradiente
- loadExample()            // Carrega exemplos pré-definidos
- setupEventListeners()    // Configura todos os event listeners
```

### Padrões de Design
- **Debouncing**: Evita atualizações excessivas durante digitação
- **Event Delegation**: Gerenciamento eficiente de eventos
- **Modular**: Funções separadas por responsabilidade
- **Responsivo**: Design que funciona em todos os dispositivos

## 🐛 Solução de Problemas

### QR Code Não Aparece
1. Verifique se os dados não estão vazios
2. Confirme que o navegador suporte ES6 modules
3. Verifique o console para erros JavaScript

### Imagem Não Carrega
1. Verifique se a URL da imagem está correta
2. Configure Cross Origin como "anonymous" para imagens externas
3. Use Data URLs para imagens locais

### Download Não Funciona
1. Verifique se o QR Code foi gerado com sucesso
2. Teste diferentes formatos (PNG, SVG)
3. Confirme que o navegador permite downloads

### Performance Lenta
1. Reduza o tamanho do QR Code
2. Desative "Arredondar tamanho dos pontos" para melhor performance
3. Evite gradientes muito complexos em QR Codes grandes

## 🌐 Compatibilidade

### Navegadores Testados
- ✅ Chrome 61+
- ✅ Firefox 60+
- ✅ Safari 10.1+
- ✅ Edge 16+

### Funcionalidades por Navegador
- **ES6 Modules**: Todos os navegadores suportados
- **CSS Grid**: Todos os navegadores suportados
- **Canvas/SVG**: Suporte completo
- **Download**: Funciona em todos os navegadores

## 📚 Exemplos de Uso

### QR Code Básico
```javascript
// Configuração mínima
{
    width: 300,
    height: 300,
    data: "https://exemplo.com",
    dotsOptions: {
        type: "square",
        color: "#000000"
    }
}
```

### QR Code Avançado
```javascript
// Configuração completa
{
    width: 400,
    height: 400,
    data: "https://minhaempresa.com",
    image: "logo.png",
    dotsOptions: {
        type: "classy-rounded",
        gradient: {
            type: "linear",
            rotation: Math.PI / 4,
            colorStops: [
                { offset: 0, color: "#ff6b6b" },
                { offset: 1, color: "#4ecdc4" }
            ]
        }
    },
    cornersSquareOptions: {
        type: "extra-rounded",
        color: "#e74c3c"
    },
    imageOptions: {
        hideBackgroundDots: true,
        imageSize: 0.3
    }
}
```

## 🚀 Próximos Passos

Após testar a demo, você pode:

1. **Integrar em seu projeto**: Copie as configurações geradas
2. **Personalizar ainda mais**: Modifique o código para suas necessidades
3. **Contribuir**: Sugira melhorias ou reporte bugs
4. **Compartilhar**: Mostre seus QR Codes personalizados

---

**Esta demo demonstra o poder e flexibilidade da QR Library Standalone!** 🎉
