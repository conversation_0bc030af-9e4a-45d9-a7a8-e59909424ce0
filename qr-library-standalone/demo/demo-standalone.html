<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>QR Library Standalone - Demo Completa</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          'Helvetica Neue', Arial, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: #333;
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
        color: white;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .header p {
        font-size: 1.1rem;
        opacity: 0.9;
      }

      .main-content {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 30px;
        background: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      }

      @media (max-width: 1024px) {
        .main-content {
          grid-template-columns: 1fr;
          gap: 20px;
        }
      }

      .controls-panel {
        max-height: 80vh;
        overflow-y: auto;
        padding-right: 10px;
      }

      .controls-panel::-webkit-scrollbar {
        width: 8px;
      }

      .controls-panel::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      .controls-panel::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
      }

      .controls-panel::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }

      .section {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
      }

      .section h3 {
        color: #495057;
        margin-bottom: 15px;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .examples-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
        margin-bottom: 15px;
      }

      .btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-align: center;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      }

      .btn-small {
        padding: 6px 12px;
        font-size: 0.8rem;
      }

      .btn-group {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }

      .form-group {
        margin-bottom: 15px;
      }

      .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #495057;
      }

      .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        font-size: 0.9rem;
        transition: border-color 0.3s ease;
      }

      .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
      }

      .checkbox-group {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 15px;
      }

      .checkbox-group input[type='checkbox'] {
        width: 18px;
        height: 18px;
        accent-color: #667eea;
      }

      .range-value {
        font-weight: 600;
        color: #667eea;
      }

      .gradient-controls {
        display: none;
        margin-top: 15px;
        padding: 15px;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #dee2e6;
      }

      .gradient-controls.active {
        display: block;
      }

      .color-stops {
        margin-bottom: 15px;
      }

      .color-stop {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 6px;
      }

      .color-stop input[type='color'] {
        width: 40px;
        height: 30px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }

      .color-stop input[type='range'] {
        flex: 1;
      }

      .color-stop button {
        background: #dc3545;
        color: white;
        border: none;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .preview-panel {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
      }

      .qr-preview {
        width: 350px;
        height: 350px;
        border: 2px dashed #dee2e6;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        position: relative;
        overflow: hidden;
      }

      .qr-preview.loading {
        border-color: #667eea;
        background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
          linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
          linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
          linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
        background-size: 20px 20px;
        background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        animation: loading-bg 1s linear infinite;
      }

      .qr-preview.success {
        border-color: #28a745;
        background: #f8fff9;
      }

      .qr-preview.error {
        border-color: #dc3545;
        background: #fff5f5;
      }

      @keyframes loading-bg {
        0% {
          background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        100% {
          background-position: 20px 20px, 20px 30px, 30px 10px, 10px 20px;
        }
      }

      .status-message {
        text-align: center;
        padding: 20px;
        font-weight: 500;
      }

      .status-message.loading {
        color: #667eea;
      }

      .status-message.success {
        color: #28a745;
      }

      .status-message.error {
        color: #dc3545;
      }

      .loading-spinner {
        width: 30px;
        height: 30px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .download-section {
        text-align: center;
      }

      .download-section h4 {
        margin-bottom: 15px;
        color: #495057;
      }

      .error-message,
      .success-message {
        padding: 15px;
        border-radius: 8px;
        font-weight: 500;
        text-align: center;
      }

      .error-message {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .success-message {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .header h1 {
          font-size: 2rem;
        }

        .main-content {
          padding: 20px;
        }

        .form-row {
          grid-template-columns: 1fr;
        }

        .examples-grid {
          grid-template-columns: repeat(2, 1fr);
        }

        .qr-preview {
          width: 280px;
          height: 280px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🎨 QR Library Standalone</h1>
        <p>Interface completa para testar e personalizar QR Codes</p>
      </div>

      <div class="main-content">
        <div class="controls-panel">
          <!-- Exemplos Rápidos -->
          <div class="section">
            <h3>📋 Exemplos Rápidos</h3>
            <div class="examples-grid">
              <button class="btn" onclick="loadExample('basic')">Básico</button>
              <button class="btn" onclick="loadExample('gradient')">
                Gradiente
              </button>
              <button class="btn" onclick="loadExample('corporate')">
                Corporativo
              </button>
              <button class="btn" onclick="loadExample('modern')">
                Moderno
              </button>
              <button class="btn" onclick="loadExample('colorful')">
                Colorido
              </button>
              <button class="btn" onclick="loadExample('minimal')">
                Minimalista
              </button>
            </div>
          </div>

          <!-- Configurações Básicas -->
          <div class="section">
            <h3>⚙️ Configurações Básicas</h3>

            <div class="form-group">
              <label for="qr-data">Dados do QR Code:</label>
              <textarea
                id="qr-data"
                class="form-control"
                rows="3"
                placeholder="Digite o texto, URL ou dados para o QR Code"
              >
https://github.com/qr-code-styling</textarea
              >
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="qr-type">Tipo de Renderização:</label>
                <select id="qr-type" class="form-control">
                  <option value="svg">SVG</option>
                  <option value="canvas">Canvas</option>
                </select>
              </div>

              <div class="form-group">
                <label for="qr-shape">Forma:</label>
                <select id="qr-shape" class="form-control">
                  <option value="square">Quadrado</option>
                  <option value="circle">Círculo</option>
                </select>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="qr-width"
                  >Largura:
                  <span class="range-value" id="width-value">300</span>px</label
                >
                <input
                  type="range"
                  id="qr-width"
                  class="form-control"
                  min="100"
                  max="800"
                  value="300"
                />
              </div>

              <div class="form-group">
                <label for="qr-height"
                  >Altura:
                  <span class="range-value" id="height-value">300</span
                  >px</label
                >
                <input
                  type="range"
                  id="qr-height"
                  class="form-control"
                  min="100"
                  max="800"
                  value="300"
                />
              </div>
            </div>

            <div class="form-group">
              <label for="qr-margin"
                >Margem:
                <span class="range-value" id="margin-value">0</span>px</label
              >
              <input
                type="range"
                id="qr-margin"
                class="form-control"
                min="0"
                max="50"
                value="0"
              />
            </div>
          </div>
        </div>

        <!-- Painel de Preview -->
        <div class="preview-panel">
          <div class="qr-preview" id="qr-preview">
            <div class="status-message" id="status-message">
              <div class="loading-spinner"></div>
              Carregando biblioteca...
            </div>
          </div>

          <div
            class="download-section"
            id="download-section"
            style="display: none"
          >
            <h4>📥 Download</h4>
            <div class="btn-group">
              <button class="btn" onclick="downloadQR('png')">📄 PNG</button>
              <button class="btn" onclick="downloadQR('jpeg')">🖼️ JPEG</button>
              <button class="btn" onclick="downloadQR('svg')">📐 SVG</button>
            </div>
          </div>

          <div id="error-container" style="display: none; margin-top: 20px">
            <div class="error-message" id="error-message"></div>
          </div>

          <div id="success-container" style="display: none; margin-top: 20px">
            <div class="success-message" id="success-message"></div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // QR Library Standalone - Versão simplificada inline
      // Para evitar problemas CORS com ES6 modules

      // Variáveis globais
      let qrCode = null;
      let updateTimeout = null;

      // Função simples para gerar QR Code usando uma biblioteca externa
      function createSimpleQR(text, size = 300) {
        // Usando QR.js - biblioteca simples que pode ser incluída via CDN
        const qr = qrcode(0, 'M');
        qr.addData(text);
        qr.make();

        const cellSize = Math.floor(size / qr.getModuleCount());
        const margin = 10;

        // Criar SVG
        const svg = document.createElementNS(
          'http://www.w3.org/2000/svg',
          'svg'
        );
        svg.setAttribute('width', size);
        svg.setAttribute('height', size);
        svg.setAttribute('viewBox', `0 0 ${size} ${size}`);

        // Fundo branco
        const bg = document.createElementNS(
          'http://www.w3.org/2000/svg',
          'rect'
        );
        bg.setAttribute('width', size);
        bg.setAttribute('height', size);
        bg.setAttribute('fill', '#ffffff');
        svg.appendChild(bg);

        // Desenhar módulos
        const moduleCount = qr.getModuleCount();
        const actualSize = size - margin * 2;
        const moduleSize = actualSize / moduleCount;

        for (let row = 0; row < moduleCount; row++) {
          for (let col = 0; col < moduleCount; col++) {
            if (qr.isDark(row, col)) {
              const rect = document.createElementNS(
                'http://www.w3.org/2000/svg',
                'rect'
              );
              rect.setAttribute('x', margin + col * moduleSize);
              rect.setAttribute('y', margin + row * moduleSize);
              rect.setAttribute('width', moduleSize);
              rect.setAttribute('height', moduleSize);
              rect.setAttribute('fill', '#000000');
              svg.appendChild(rect);
            }
          }
        }

        return svg;
      }

      // Função para mostrar status
      function showStatus(message, type = 'loading') {
        const statusElement = document.getElementById('status-message');
        const previewElement = document.getElementById('qr-preview');

        if (statusElement) {
          statusElement.innerHTML =
            type === 'loading'
              ? `<div class="loading-spinner"></div>${message}`
              : message;
          statusElement.className = `status-message ${type}`;
        }

        if (previewElement) {
          previewElement.className = `qr-preview ${type}`;
        }

        // Ocultar/mostrar seções
        const downloadSection = document.getElementById('download-section');
        const errorContainer = document.getElementById('error-container');
        const successContainer = document.getElementById('success-container');

        if (downloadSection)
          downloadSection.style.display = type === 'success' ? 'block' : 'none';
        if (errorContainer) errorContainer.style.display = 'none';
        if (successContainer) successContainer.style.display = 'none';
      }

      // Função para mostrar erro
      function showError(error) {
        showStatus(`Erro: ${error}`, 'error');
        const errorMessage = document.getElementById('error-message');
        const errorContainer = document.getElementById('error-container');

        if (errorMessage) errorMessage.textContent = error;
        if (errorContainer) errorContainer.style.display = 'block';
        console.error('Erro detalhado:', error);
      }

      // Função para mostrar sucesso
      function showSuccess(message) {
        const successMessage = document.getElementById('success-message');
        const successContainer = document.getElementById('success-container');

        if (successMessage) successMessage.textContent = message;
        if (successContainer) {
          successContainer.style.display = 'block';
          setTimeout(() => {
            successContainer.style.display = 'none';
          }, 3000);
        }
      }

      // Função para atualizar valores de range
      function updateRangeValue(rangeId, valueId, suffix = '') {
        const range = document.getElementById(rangeId);
        const valueSpan = document.getElementById(valueId);

        if (range && valueSpan) {
          const updateValue = () => {
            let value = range.value;
            valueSpan.textContent = value + suffix;
          };

          updateValue();
          range.addEventListener('input', updateValue);
        }
      }

      // Função para atualizar QR Code
      function updateQRCode() {
        if (updateTimeout) {
          clearTimeout(updateTimeout);
        }

        updateTimeout = setTimeout(async () => {
          try {
            showStatus('Gerando QR Code...', 'loading');

            const data =
              document.getElementById('qr-data').value ||
              'https://github.com/qr-code-styling';
            const width = parseInt(document.getElementById('qr-width').value);

            // Limpar container
            const container = document.getElementById('qr-preview');
            container.innerHTML = '';

            // Criar QR Code simples
            const qrSvg = createSimpleQR(data, width);
            container.appendChild(qrSvg);

            // Salvar referência para download
            qrCode = qrSvg;

            showStatus('QR Code gerado com sucesso!', 'success');
          } catch (error) {
            showError(error.message || error);
          }
        }, 300);
      }

      // Função para download
      function downloadQR(extension) {
        if (!qrCode) {
          showError('Gere um QR Code primeiro!');
          return;
        }

        try {
          const name = 'qr-code-' + Date.now();

          if (extension === 'svg') {
            // Download SVG
            const svgData = new XMLSerializer().serializeToString(qrCode);
            const svgBlob = new Blob([svgData], {
              type: 'image/svg+xml;charset=utf-8',
            });
            const url = URL.createObjectURL(svgBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = name + '.svg';
            link.click();

            URL.revokeObjectURL(url);
          } else {
            // Download PNG/JPEG via Canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const size = parseInt(document.getElementById('qr-width').value);

            canvas.width = size;
            canvas.height = size;

            // Converter SVG para Canvas
            const svgData = new XMLSerializer().serializeToString(qrCode);
            const img = new Image();

            img.onload = function () {
              ctx.drawImage(img, 0, 0);

              const link = document.createElement('a');
              link.href = canvas.toDataURL(`image/${extension}`, 0.9);
              link.download = name + '.' + extension;
              link.click();
            };

            img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
          }

          showSuccess(`Download ${extension.toUpperCase()} iniciado!`);
        } catch (error) {
          showError(error.message || error);
        }
      }

      // Função para carregar exemplos
      function loadExample(type) {
        switch (type) {
          case 'basic':
            document.getElementById('qr-data').value =
              'https://github.com/qr-code-styling';
            break;
          case 'gradient':
            document.getElementById('qr-data').value =
              'QR Code com gradiente linear!';
            break;
          case 'corporate':
            document.getElementById('qr-data').value =
              'https://minhaempresa.com';
            break;
          case 'modern':
            document.getElementById('qr-data').value =
              'Design moderno e elegante';
            break;
          case 'colorful':
            document.getElementById('qr-data').value =
              'QR Code colorido e vibrante!';
            break;
          case 'minimal':
            document.getElementById('qr-data').value =
              'Minimalismo é elegância';
            document.getElementById('qr-margin').value = '20';
            break;
        }

        updateAllRangeValues();
        updateQRCode();
      }

      // Função para atualizar todos os valores de range
      function updateAllRangeValues() {
        updateRangeValue('qr-width', 'width-value', 'px');
        updateRangeValue('qr-height', 'height-value', 'px');
        updateRangeValue('qr-margin', 'margin-value', 'px');
      }

      // Event listeners
      function setupEventListeners() {
        document
          .getElementById('qr-data')
          .addEventListener('input', updateQRCode);
        document
          .getElementById('qr-type')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('qr-shape')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('qr-width')
          .addEventListener('input', updateQRCode);
        document
          .getElementById('qr-height')
          .addEventListener('input', updateQRCode);
        document
          .getElementById('qr-margin')
          .addEventListener('input', updateQRCode);
      }

      // Inicialização
      async function init() {
        try {
          showStatus('Carregando biblioteca QR Code...', 'loading');

          // Configurar event listeners
          setupEventListeners();

          // Atualizar valores de range
          updateAllRangeValues();

          // Aguardar carregamento da biblioteca QR
          await new Promise((resolve) => setTimeout(resolve, 500));

          // Gerar QR Code inicial
          updateQRCode();
        } catch (error) {
          showError(error.message || error);
        }
      }

      // Inicializar quando a página carregar
      document.addEventListener('DOMContentLoaded', init);
    </script>

    <!-- Biblioteca QR Code simples via CDN -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js"></script>
  </body>
</html>
