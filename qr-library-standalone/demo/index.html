<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>QR Library Standalone - Demo Completa</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          Oxygen, Ubuntu, Cantarell, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #4267b2 0%, #365899 100%);
        color: white;
        padding: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        font-weight: 700;
      }

      .header p {
        font-size: 1.1rem;
        opacity: 0.9;
      }

      .main-content {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 0;
        min-height: 800px;
      }

      .controls-panel {
        background: #f8f9fa;
        padding: 30px;
        overflow-y: auto;
        max-height: 800px;
      }

      .preview-panel {
        padding: 30px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: white;
        border-left: 1px solid #e9ecef;
      }

      .section {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        border: 1px solid #e9ecef;
      }

      .section h3 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.2rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .section h3::before {
        content: '';
        width: 4px;
        height: 20px;
        background: #4267b2;
        border-radius: 2px;
      }

      .form-group {
        margin-bottom: 15px;
      }

      .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #495057;
        font-size: 0.9rem;
      }

      .form-control {
        width: 100%;
        padding: 10px 12px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.3s ease;
        background: white;
      }

      .form-control:focus {
        outline: none;
        border-color: #4267b2;
        box-shadow: 0 0 0 3px rgba(66, 103, 178, 0.1);
      }

      .form-control:hover {
        border-color: #ced4da;
      }

      select.form-control {
        cursor: pointer;
      }

      input[type='color'] {
        height: 40px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        cursor: pointer;
        padding: 2px;
      }

      input[type='range'] {
        width: 100%;
        height: 6px;
        border-radius: 3px;
        background: #e9ecef;
        outline: none;
        -webkit-appearance: none;
      }

      input[type='range']::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #4267b2;
        cursor: pointer;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
      }

      input[type='range']::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #4267b2;
        cursor: pointer;
        border: none;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
      }

      .checkbox-group {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .checkbox-group input[type='checkbox'] {
        width: 18px;
        height: 18px;
        accent-color: #4267b2;
      }

      .gradient-controls {
        display: none;
        margin-top: 15px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
      }

      .gradient-controls.active {
        display: block;
      }

      .color-stops {
        margin-top: 15px;
      }

      .color-stop {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
        padding: 10px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e9ecef;
      }

      .color-stop input[type='color'] {
        width: 40px;
        height: 30px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }

      .color-stop input[type='range'] {
        flex: 1;
      }

      .color-stop button {
        background: #dc3545;
        color: white;
        border: none;
        padding: 5px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
      }

      .btn {
        background: #4267b2;
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
      }

      .btn:hover {
        background: #365899;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(66, 103, 178, 0.3);
      }

      .btn:active {
        transform: translateY(0);
      }

      .btn-secondary {
        background: #6c757d;
      }

      .btn-secondary:hover {
        background: #5a6268;
      }

      .btn-success {
        background: #28a745;
      }

      .btn-success:hover {
        background: #218838;
      }

      .btn-small {
        padding: 8px 12px;
        font-size: 12px;
      }

      .btn-group {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-top: 15px;
      }

      .qr-preview {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 12px;
        padding: 30px;
        text-align: center;
        min-height: 350px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        transition: all 0.3s ease;
      }

      .qr-preview.has-qr {
        border-style: solid;
        border-color: #28a745;
        background: white;
      }

      .qr-preview.loading {
        border-color: #ffc107;
        background: #fff3cd;
      }

      .qr-preview.error {
        border-color: #dc3545;
        background: #f8d7da;
      }

      .status-message {
        color: #6c757d;
        font-size: 1.1rem;
        font-weight: 500;
      }

      .status-message.loading {
        color: #856404;
      }

      .status-message.error {
        color: #721c24;
      }

      .status-message.success {
        color: #155724;
      }

      .download-section {
        text-align: center;
      }

      .download-section h4 {
        margin-bottom: 15px;
        color: #2c3e50;
        font-size: 1.1rem;
      }

      .examples-section {
        margin-bottom: 20px;
      }

      .examples-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
      }

      .range-value {
        display: inline-block;
        min-width: 40px;
        text-align: center;
        font-weight: 600;
        color: #4267b2;
        background: #e3f2fd;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }

      .two-column {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
      }

      .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 10px 15px;
        border-radius: 6px;
        border: 1px solid #f5c6cb;
        margin-top: 10px;
        font-size: 14px;
      }

      .success-message {
        background: #d4edda;
        color: #155724;
        padding: 10px 15px;
        border-radius: 6px;
        border: 1px solid #c3e6cb;
        margin-top: 10px;
        font-size: 14px;
      }

      /* Responsividade */
      @media (max-width: 1024px) {
        .main-content {
          grid-template-columns: 1fr;
        }

        .preview-panel {
          border-left: none;
          border-top: 1px solid #e9ecef;
        }

        .controls-panel {
          max-height: none;
        }
      }

      @media (max-width: 768px) {
        body {
          padding: 10px;
        }

        .header {
          padding: 20px;
        }

        .header h1 {
          font-size: 2rem;
        }

        .controls-panel,
        .preview-panel {
          padding: 20px;
        }

        .two-column {
          grid-template-columns: 1fr;
        }

        .btn-group {
          justify-content: center;
        }
      }

      /* Animações */
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .section {
        animation: fadeIn 0.5s ease-out;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #4267b2;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🎨 QR Library Standalone</h1>
        <p>Interface completa para testar e personalizar QR Codes</p>
      </div>

      <div class="main-content">
        <!-- Painel de Controles -->
        <div class="controls-panel">
          <!-- Exemplos Pré-definidos -->
          <div class="section examples-section">
            <h3>📋 Exemplos Rápidos</h3>
            <div class="examples-grid">
              <button class="btn btn-small" onclick="loadExample('basic')">
                Básico
              </button>
              <button class="btn btn-small" onclick="loadExample('gradient')">
                Gradiente
              </button>
              <button class="btn btn-small" onclick="loadExample('corporate')">
                Corporativo
              </button>
              <button class="btn btn-small" onclick="loadExample('modern')">
                Moderno
              </button>
              <button class="btn btn-small" onclick="loadExample('colorful')">
                Colorido
              </button>
              <button class="btn btn-small" onclick="loadExample('minimal')">
                Minimalista
              </button>
            </div>
          </div>

          <!-- Configurações Básicas -->
          <div class="section">
            <h3>⚙️ Configurações Básicas</h3>

            <div class="form-group">
              <label for="qr-data">Dados do QR Code:</label>
              <textarea
                id="qr-data"
                class="form-control"
                rows="3"
                placeholder="Digite o texto, URL ou dados para o QR Code"
              >
https://github.com/qr-code-styling</textarea
              >
            </div>

            <div class="two-column">
              <div class="form-group">
                <label for="qr-type">Tipo de Renderização:</label>
                <select id="qr-type" class="form-control">
                  <option value="svg">SVG</option>
                  <option value="canvas">Canvas</option>
                </select>
              </div>

              <div class="form-group">
                <label for="qr-shape">Forma:</label>
                <select id="qr-shape" class="form-control">
                  <option value="square">Quadrado</option>
                  <option value="circle">Círculo</option>
                </select>
              </div>
            </div>

            <div class="two-column">
              <div class="form-group">
                <label for="qr-width"
                  >Largura:
                  <span class="range-value" id="width-value">300</span>px</label
                >
                <input
                  type="range"
                  id="qr-width"
                  class="form-control"
                  min="100"
                  max="800"
                  value="300"
                />
              </div>

              <div class="form-group">
                <label for="qr-height"
                  >Altura:
                  <span class="range-value" id="height-value">300</span
                  >px</label
                >
                <input
                  type="range"
                  id="qr-height"
                  class="form-control"
                  min="100"
                  max="800"
                  value="300"
                />
              </div>
            </div>

            <div class="form-group">
              <label for="qr-margin"
                >Margem:
                <span class="range-value" id="margin-value">0</span>px</label
              >
              <input
                type="range"
                id="qr-margin"
                class="form-control"
                min="0"
                max="50"
                value="0"
              />
            </div>
          </div>

          <!-- Configurações QR -->
          <div class="section">
            <h3>🔧 Configurações QR</h3>

            <div class="two-column">
              <div class="form-group">
                <label for="error-correction">Correção de Erro:</label>
                <select id="error-correction" class="form-control">
                  <option value="L">L (~7%)</option>
                  <option value="M">M (~15%)</option>
                  <option value="Q" selected>Q (~25%)</option>
                  <option value="H">H (~30%)</option>
                </select>
              </div>

              <div class="form-group">
                <label for="qr-mode">Modo de Codificação:</label>
                <select id="qr-mode" class="form-control">
                  <option value="">Automático</option>
                  <option value="Numeric">Numérico</option>
                  <option value="Alphanumeric">Alfanumérico</option>
                  <option value="Byte">Byte</option>
                  <option value="Kanji">Kanji</option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label for="type-number"
                >Versão QR:
                <span class="range-value" id="type-number-value"
                  >0 (Auto)</span
                ></label
              >
              <input
                type="range"
                id="type-number"
                class="form-control"
                min="0"
                max="40"
                value="0"
              />
            </div>
          </div>

          <!-- Estilização de Pontos -->
          <div class="section">
            <h3>⚫ Estilização de Pontos</h3>

            <div class="form-group">
              <label for="dots-type">Tipo dos Pontos:</label>
              <select id="dots-type" class="form-control">
                <option value="square">Quadrado</option>
                <option value="dots">Círculos</option>
                <option value="rounded">Arredondado</option>
                <option value="extra-rounded">Extra Arredondado</option>
                <option value="classy">Elegante</option>
                <option value="classy-rounded">Elegante Arredondado</option>
              </select>
            </div>

            <div class="checkbox-group">
              <input type="checkbox" id="dots-round-size" checked />
              <label for="dots-round-size">Arredondar tamanho dos pontos</label>
            </div>

            <div class="form-group" style="margin-top: 15px">
              <div class="checkbox-group">
                <input type="checkbox" id="dots-use-gradient" />
                <label for="dots-use-gradient">Usar gradiente nos pontos</label>
              </div>
            </div>

            <div class="form-group" id="dots-solid-color">
              <label for="dots-color">Cor dos Pontos:</label>
              <input
                type="color"
                id="dots-color"
                class="form-control"
                value="#000000"
              />
            </div>

            <div class="gradient-controls" id="dots-gradient-controls">
              <div class="form-group">
                <label for="dots-gradient-type">Tipo de Gradiente:</label>
                <select id="dots-gradient-type" class="form-control">
                  <option value="linear">Linear</option>
                  <option value="radial">Radial</option>
                </select>
              </div>

              <div class="form-group" id="dots-gradient-rotation-group">
                <label for="dots-gradient-rotation"
                  >Rotação:
                  <span class="range-value" id="dots-rotation-value"
                    >0°</span
                  ></label
                >
                <input
                  type="range"
                  id="dots-gradient-rotation"
                  class="form-control"
                  min="0"
                  max="360"
                  value="0"
                />
              </div>

              <div class="color-stops" id="dots-color-stops">
                <label>Paradas de Cor:</label>
                <div class="color-stop">
                  <input type="color" value="#ff6b6b" />
                  <input type="range" min="0" max="1" step="0.01" value="0" />
                  <span class="range-value">0%</span>
                  <button
                    type="button"
                    onclick="removeColorStop(this)"
                    style="display: none"
                  >
                    ×
                  </button>
                </div>
                <div class="color-stop">
                  <input type="color" value="#4ecdc4" />
                  <input type="range" min="0" max="1" step="0.01" value="1" />
                  <span class="range-value">100%</span>
                  <button
                    type="button"
                    onclick="removeColorStop(this)"
                    style="display: none"
                  >
                    ×
                  </button>
                </div>
              </div>

              <button
                type="button"
                class="btn btn-small"
                onclick="addColorStop('dots')"
              >
                + Adicionar Cor
              </button>
            </div>
          </div>

          <!-- Cantos Quadrados -->
          <div class="section">
            <h3>⬜ Cantos Quadrados</h3>

            <div class="checkbox-group">
              <input type="checkbox" id="corners-square-enabled" />
              <label for="corners-square-enabled"
                >Personalizar cantos quadrados</label
              >
            </div>

            <div
              id="corners-square-options"
              style="display: none; margin-top: 15px"
            >
              <div class="form-group">
                <label for="corners-square-type">Tipo:</label>
                <select id="corners-square-type" class="form-control">
                  <option value="square">Quadrado</option>
                  <option value="dot">Círculo</option>
                  <option value="extra-rounded">Extra Arredondado</option>
                </select>
              </div>

              <div class="checkbox-group">
                <input type="checkbox" id="corners-square-use-gradient" />
                <label for="corners-square-use-gradient">Usar gradiente</label>
              </div>

              <div class="form-group" id="corners-square-solid-color">
                <label for="corners-square-color">Cor:</label>
                <input
                  type="color"
                  id="corners-square-color"
                  class="form-control"
                  value="#000000"
                />
              </div>

              <div
                class="gradient-controls"
                id="corners-square-gradient-controls"
              >
                <div class="form-group">
                  <label for="corners-square-gradient-type"
                    >Tipo de Gradiente:</label
                  >
                  <select
                    id="corners-square-gradient-type"
                    class="form-control"
                  >
                    <option value="linear">Linear</option>
                    <option value="radial">Radial</option>
                  </select>
                </div>

                <div
                  class="form-group"
                  id="corners-square-gradient-rotation-group"
                >
                  <label for="corners-square-gradient-rotation"
                    >Rotação:
                    <span class="range-value" id="corners-square-rotation-value"
                      >0°</span
                    ></label
                  >
                  <input
                    type="range"
                    id="corners-square-gradient-rotation"
                    class="form-control"
                    min="0"
                    max="360"
                    value="0"
                  />
                </div>

                <div class="color-stops" id="corners-square-color-stops">
                  <label>Paradas de Cor:</label>
                  <div class="color-stop">
                    <input type="color" value="#ff6b6b" />
                    <input type="range" min="0" max="1" step="0.01" value="0" />
                    <span class="range-value">0%</span>
                    <button
                      type="button"
                      onclick="removeColorStop(this)"
                      style="display: none"
                    >
                      ×
                    </button>
                  </div>
                  <div class="color-stop">
                    <input type="color" value="#4ecdc4" />
                    <input type="range" min="0" max="1" step="0.01" value="1" />
                    <span class="range-value">100%</span>
                    <button
                      type="button"
                      onclick="removeColorStop(this)"
                      style="display: none"
                    >
                      ×
                    </button>
                  </div>
                </div>

                <button
                  type="button"
                  class="btn btn-small"
                  onclick="addColorStop('corners-square')"
                >
                  + Adicionar Cor
                </button>
              </div>
            </div>
          </div>

          <!-- Pontos dos Cantos -->
          <div class="section">
            <h3>🔘 Pontos dos Cantos</h3>

            <div class="checkbox-group">
              <input type="checkbox" id="corners-dot-enabled" />
              <label for="corners-dot-enabled"
                >Personalizar pontos dos cantos</label
              >
            </div>

            <div
              id="corners-dot-options"
              style="display: none; margin-top: 15px"
            >
              <div class="form-group">
                <label for="corners-dot-type">Tipo:</label>
                <select id="corners-dot-type" class="form-control">
                  <option value="square">Quadrado</option>
                  <option value="dot">Círculo</option>
                </select>
              </div>

              <div class="checkbox-group">
                <input type="checkbox" id="corners-dot-use-gradient" />
                <label for="corners-dot-use-gradient">Usar gradiente</label>
              </div>

              <div class="form-group" id="corners-dot-solid-color">
                <label for="corners-dot-color">Cor:</label>
                <input
                  type="color"
                  id="corners-dot-color"
                  class="form-control"
                  value="#000000"
                />
              </div>

              <div class="gradient-controls" id="corners-dot-gradient-controls">
                <div class="form-group">
                  <label for="corners-dot-gradient-type"
                    >Tipo de Gradiente:</label
                  >
                  <select id="corners-dot-gradient-type" class="form-control">
                    <option value="linear">Linear</option>
                    <option value="radial">Radial</option>
                  </select>
                </div>

                <div
                  class="form-group"
                  id="corners-dot-gradient-rotation-group"
                >
                  <label for="corners-dot-gradient-rotation"
                    >Rotação:
                    <span class="range-value" id="corners-dot-rotation-value"
                      >0°</span
                    ></label
                  >
                  <input
                    type="range"
                    id="corners-dot-gradient-rotation"
                    class="form-control"
                    min="0"
                    max="360"
                    value="0"
                  />
                </div>

                <div class="color-stops" id="corners-dot-color-stops">
                  <label>Paradas de Cor:</label>
                  <div class="color-stop">
                    <input type="color" value="#ff6b6b" />
                    <input type="range" min="0" max="1" step="0.01" value="0" />
                    <span class="range-value">0%</span>
                    <button
                      type="button"
                      onclick="removeColorStop(this)"
                      style="display: none"
                    >
                      ×
                    </button>
                  </div>
                  <div class="color-stop">
                    <input type="color" value="#4ecdc4" />
                    <input type="range" min="0" max="1" step="0.01" value="1" />
                    <span class="range-value">100%</span>
                    <button
                      type="button"
                      onclick="removeColorStop(this)"
                      style="display: none"
                    >
                      ×
                    </button>
                  </div>
                </div>

                <button
                  type="button"
                  class="btn btn-small"
                  onclick="addColorStop('corners-dot')"
                >
                  + Adicionar Cor
                </button>
              </div>
            </div>
          </div>

          <!-- Configurações de Fundo -->
          <div class="section">
            <h3>🎨 Configurações de Fundo</h3>

            <div class="checkbox-group">
              <input type="checkbox" id="background-use-gradient" />
              <label for="background-use-gradient"
                >Usar gradiente no fundo</label
              >
            </div>

            <div class="form-group" id="background-solid-color">
              <label for="background-color">Cor de Fundo:</label>
              <input
                type="color"
                id="background-color"
                class="form-control"
                value="#ffffff"
              />
            </div>

            <div class="form-group">
              <label for="background-round"
                >Arredondamento:
                <span class="range-value" id="background-round-value"
                  >0</span
                ></label
              >
              <input
                type="range"
                id="background-round"
                class="form-control"
                min="0"
                max="1"
                step="0.01"
                value="0"
              />
            </div>

            <div class="gradient-controls" id="background-gradient-controls">
              <div class="form-group">
                <label for="background-gradient-type">Tipo de Gradiente:</label>
                <select id="background-gradient-type" class="form-control">
                  <option value="linear">Linear</option>
                  <option value="radial">Radial</option>
                </select>
              </div>

              <div class="form-group" id="background-gradient-rotation-group">
                <label for="background-gradient-rotation"
                  >Rotação:
                  <span class="range-value" id="background-rotation-value"
                    >0°</span
                  ></label
                >
                <input
                  type="range"
                  id="background-gradient-rotation"
                  class="form-control"
                  min="0"
                  max="360"
                  value="0"
                />
              </div>

              <div class="color-stops" id="background-color-stops">
                <label>Paradas de Cor:</label>
                <div class="color-stop">
                  <input type="color" value="#667eea" />
                  <input type="range" min="0" max="1" step="0.01" value="0" />
                  <span class="range-value">0%</span>
                  <button
                    type="button"
                    onclick="removeColorStop(this)"
                    style="display: none"
                  >
                    ×
                  </button>
                </div>
                <div class="color-stop">
                  <input type="color" value="#764ba2" />
                  <input type="range" min="0" max="1" step="0.01" value="1" />
                  <span class="range-value">100%</span>
                  <button
                    type="button"
                    onclick="removeColorStop(this)"
                    style="display: none"
                  >
                    ×
                  </button>
                </div>
              </div>

              <button
                type="button"
                class="btn btn-small"
                onclick="addColorStop('background')"
              >
                + Adicionar Cor
              </button>
            </div>
          </div>

          <!-- Configurações de Imagem -->
          <div class="section">
            <h3>🖼️ Imagem/Logo Central</h3>

            <div class="checkbox-group">
              <input type="checkbox" id="image-enabled" />
              <label for="image-enabled">Adicionar imagem/logo</label>
            </div>

            <div id="image-options" style="display: none; margin-top: 15px">
              <div class="form-group">
                <label for="image-url">URL da Imagem:</label>
                <input
                  type="text"
                  id="image-url"
                  class="form-control"
                  placeholder="https://exemplo.com/logo.png ou data:image/..."
                />
              </div>

              <div class="form-group">
                <label for="image-size"
                  >Tamanho da Imagem:
                  <span class="range-value" id="image-size-value"
                    >0.4</span
                  ></label
                >
                <input
                  type="range"
                  id="image-size"
                  class="form-control"
                  min="0.1"
                  max="0.8"
                  step="0.01"
                  value="0.4"
                />
              </div>

              <div class="form-group">
                <label for="image-margin"
                  >Margem da Imagem:
                  <span class="range-value" id="image-margin-value">0</span
                  >px</label
                >
                <input
                  type="range"
                  id="image-margin"
                  class="form-control"
                  min="0"
                  max="20"
                  value="0"
                />
              </div>

              <div class="checkbox-group">
                <input type="checkbox" id="image-hide-background" checked />
                <label for="image-hide-background"
                  >Ocultar pontos atrás da imagem</label
                >
              </div>

              <div class="form-group">
                <label for="image-cross-origin">Cross Origin:</label>
                <select id="image-cross-origin" class="form-control">
                  <option value="">Padrão</option>
                  <option value="anonymous">Anonymous</option>
                  <option value="use-credentials">Use Credentials</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Painel de Preview -->
        <div class="preview-panel">
          <div class="qr-preview" id="qr-preview">
            <div class="status-message" id="status-message">
              <div class="loading-spinner"></div>
              Carregando biblioteca...
            </div>
          </div>

          <div
            class="download-section"
            id="download-section"
            style="display: none"
          >
            <h4>📥 Download</h4>
            <div class="btn-group">
              <button class="btn" onclick="downloadQR('png')">📄 PNG</button>
              <button class="btn" onclick="downloadQR('jpeg')">🖼️ JPEG</button>
              <button class="btn" onclick="downloadQR('svg')">📐 SVG</button>
            </div>
          </div>

          <div id="error-container" style="display: none; margin-top: 20px">
            <div class="error-message" id="error-message"></div>
          </div>

          <div id="success-container" style="display: none; margin-top: 20px">
            <div class="success-message" id="success-message"></div>
          </div>
        </div>
      </div>
    </div>

    <script type="module">
      // Importar a biblioteca QR Code Styling
      import QRCodeStyling from '../index.js';

      // Variáveis globais
      let qrCode = null;
      let updateTimeout = null;

      // Função para mostrar status
      function showStatus(message, type = 'loading') {
        const statusElement = document.getElementById('status-message');
        const previewElement = document.getElementById('qr-preview');

        statusElement.innerHTML =
          type === 'loading'
            ? `<div class="loading-spinner"></div>${message}`
            : message;
        statusElement.className = `status-message ${type}`;

        previewElement.className = `qr-preview ${type}`;

        // Ocultar/mostrar seções
        document.getElementById('download-section').style.display =
          type === 'success' ? 'block' : 'none';
        document.getElementById('error-container').style.display = 'none';
        document.getElementById('success-container').style.display = 'none';
      }

      // Função para mostrar erro
      function showError(error) {
        showStatus(`Erro: ${error.message}`, 'error');
        document.getElementById('error-message').textContent = error.message;
        document.getElementById('error-container').style.display = 'block';
        console.error('Erro detalhado:', error);
      }

      // Função para mostrar sucesso
      function showSuccess(message) {
        document.getElementById('success-message').textContent = message;
        document.getElementById('success-container').style.display = 'block';
        setTimeout(() => {
          document.getElementById('success-container').style.display = 'none';
        }, 3000);
      }

      // Função para atualizar valores de range
      function updateRangeValue(rangeId, valueId, suffix = '') {
        const range = document.getElementById(rangeId);
        const valueSpan = document.getElementById(valueId);

        if (range && valueSpan) {
          const updateValue = () => {
            let value = range.value;
            if (rangeId === 'type-number' && value === '0') {
              value = '0 (Auto)';
            } else if (suffix === '°') {
              value = Math.round(value) + suffix;
            } else if (suffix === '%') {
              value = Math.round(parseFloat(value) * 100) + suffix;
            } else {
              value = value + suffix;
            }
            valueSpan.textContent = value;
          };

          updateValue();
          range.addEventListener('input', updateValue);
        }
      }

      // Função para coletar paradas de cor
      function getColorStops(containerId) {
        const container = document.getElementById(containerId);
        const colorStops = [];

        container.querySelectorAll('.color-stop').forEach((stop) => {
          const colorInput = stop.querySelector('input[type="color"]');
          const rangeInput = stop.querySelector('input[type="range"]');

          if (colorInput && rangeInput) {
            colorStops.push({
              offset: parseFloat(rangeInput.value),
              color: colorInput.value,
            });
          }
        });

        return colorStops.sort((a, b) => a.offset - b.offset);
      }

      // Função para criar gradiente
      function createGradient(prefix) {
        const useGradient = document.getElementById(
          `${prefix}-use-gradient`
        ).checked;
        if (!useGradient) return null;

        const type = document.getElementById(`${prefix}-gradient-type`).value;
        const rotation =
          (parseFloat(
            document.getElementById(`${prefix}-gradient-rotation`).value
          ) *
            Math.PI) /
          180;
        const colorStops = getColorStops(`${prefix}-color-stops`);

        return {
          type: type,
          rotation: type === 'linear' ? rotation : undefined,
          colorStops: colorStops,
        };
      }

      // Função para coletar todas as opções
      function collectOptions() {
        const options = {
          width: parseInt(document.getElementById('qr-width').value),
          height: parseInt(document.getElementById('qr-height').value),
          type: document.getElementById('qr-type').value,
          shape: document.getElementById('qr-shape').value,
          data:
            document.getElementById('qr-data').value ||
            'https://github.com/qr-code-styling',
          margin: parseInt(document.getElementById('qr-margin').value),

          // Configurações QR
          qrOptions: {
            typeNumber: parseInt(document.getElementById('type-number').value),
            mode: document.getElementById('qr-mode').value || undefined,
            errorCorrectionLevel:
              document.getElementById('error-correction').value,
          },

          // Opções dos pontos
          dotsOptions: {
            type: document.getElementById('dots-type').value,
            roundSize: document.getElementById('dots-round-size').checked,
          },

          // Opções de fundo
          backgroundOptions: {
            round: parseFloat(
              document.getElementById('background-round').value
            ),
          },
        };

        // Cor ou gradiente dos pontos
        const dotsGradient = createGradient('dots');
        if (dotsGradient) {
          options.dotsOptions.gradient = dotsGradient;
        } else {
          options.dotsOptions.color =
            document.getElementById('dots-color').value;
        }

        // Cantos quadrados
        if (document.getElementById('corners-square-enabled').checked) {
          options.cornersSquareOptions = {
            type: document.getElementById('corners-square-type').value,
          };

          const cornersSquareGradient = createGradient('corners-square');
          if (cornersSquareGradient) {
            options.cornersSquareOptions.gradient = cornersSquareGradient;
          } else {
            options.cornersSquareOptions.color = document.getElementById(
              'corners-square-color'
            ).value;
          }
        }

        // Pontos dos cantos
        if (document.getElementById('corners-dot-enabled').checked) {
          options.cornersDotOptions = {
            type: document.getElementById('corners-dot-type').value,
          };

          const cornersDotGradient = createGradient('corners-dot');
          if (cornersDotGradient) {
            options.cornersDotOptions.gradient = cornersDotGradient;
          } else {
            options.cornersDotOptions.color =
              document.getElementById('corners-dot-color').value;
          }
        }

        // Fundo
        const backgroundGradient = createGradient('background');
        if (backgroundGradient) {
          options.backgroundOptions.gradient = backgroundGradient;
        } else {
          options.backgroundOptions.color =
            document.getElementById('background-color').value;
        }

        // Imagem
        if (document.getElementById('image-enabled').checked) {
          const imageUrl = document.getElementById('image-url').value.trim();
          if (imageUrl) {
            options.image = imageUrl;
            options.imageOptions = {
              hideBackgroundDots: document.getElementById(
                'image-hide-background'
              ).checked,
              imageSize: parseFloat(
                document.getElementById('image-size').value
              ),
              margin: parseInt(document.getElementById('image-margin').value),
              crossOrigin:
                document.getElementById('image-cross-origin').value ||
                undefined,
            };
          }
        }

        return options;
      }

      // Função para atualizar QR Code
      function updateQRCode() {
        if (updateTimeout) {
          clearTimeout(updateTimeout);
        }

        updateTimeout = setTimeout(async () => {
          try {
            showStatus('Gerando QR Code...', 'loading');

            const options = collectOptions();

            // Limpar container
            const container = document.getElementById('qr-preview');
            container.innerHTML = '';

            // Criar novo QR Code
            qrCode = new QRCodeStyling(options);
            qrCode.append(container);

            // Aguardar um pouco para renderização
            await new Promise((resolve) => setTimeout(resolve, 100));

            showStatus('QR Code gerado com sucesso!', 'success');
          } catch (error) {
            showError(error);
          }
        }, 300); // Debounce de 300ms
      }

      // Função para download
      window.downloadQR = function (extension) {
        if (!qrCode) {
          showError(new Error('Gere um QR Code primeiro!'));
          return;
        }

        try {
          const name = 'qr-code-' + Date.now();
          qrCode.download({ name, extension });
          showSuccess(`Download ${extension.toUpperCase()} iniciado!`);
        } catch (error) {
          showError(error);
        }
      };

      // Função para adicionar parada de cor
      window.addColorStop = function (prefix) {
        const container = document.getElementById(`${prefix}-color-stops`);
        const colorStops = container.querySelectorAll('.color-stop');

        // Calcular offset para nova cor (meio do último intervalo)
        let newOffset = 0.5;
        if (colorStops.length > 0) {
          const lastStop = colorStops[colorStops.length - 1];
          const lastRange = lastStop.querySelector('input[type="range"]');
          newOffset = Math.min(1, parseFloat(lastRange.value) + 0.2);
        }

        const colorStop = document.createElement('div');
        colorStop.className = 'color-stop';
        colorStop.innerHTML = `
                <input type="color" value="#${Math.floor(
                  Math.random() * 16777215
                ).toString(16)}">
                <input type="range" min="0" max="1" step="0.01" value="${newOffset}">
                <span class="range-value">${Math.round(newOffset * 100)}%</span>
                <button type="button" onclick="removeColorStop(this)">×</button>
            `;

        container.appendChild(colorStop);

        // Adicionar event listener para o range
        const rangeInput = colorStop.querySelector('input[type="range"]');
        const valueSpan = colorStop.querySelector('.range-value');

        rangeInput.addEventListener('input', () => {
          valueSpan.textContent =
            Math.round(parseFloat(rangeInput.value) * 100) + '%';
          updateQRCode();
        });

        colorStop
          .querySelector('input[type="color"]')
          .addEventListener('change', updateQRCode);

        // Mostrar botões de remoção se houver mais de 2 cores
        updateRemoveButtons(prefix);
        updateQRCode();
      };

      // Função para remover parada de cor
      window.removeColorStop = function (button) {
        const colorStop = button.parentElement;
        const container = colorStop.parentElement;
        const prefix = container.id.replace('-color-stops', '');

        colorStop.remove();
        updateRemoveButtons(prefix);
        updateQRCode();
      };

      // Função para atualizar botões de remoção
      function updateRemoveButtons(prefix) {
        const container = document.getElementById(`${prefix}-color-stops`);
        const colorStops = container.querySelectorAll('.color-stop');
        const removeButtons = container.querySelectorAll('button');

        removeButtons.forEach((button) => {
          button.style.display = colorStops.length > 2 ? 'block' : 'none';
        });
      }

      // Função para carregar exemplos
      window.loadExample = function (type) {
        // Resetar todos os controles primeiro
        resetControls();

        switch (type) {
          case 'basic':
            document.getElementById('qr-data').value =
              'https://github.com/qr-code-styling';
            document.getElementById('dots-type').value = 'square';
            document.getElementById('dots-color').value = '#000000';
            document.getElementById('background-color').value = '#ffffff';
            break;

          case 'gradient':
            document.getElementById('qr-data').value =
              'QR Code com gradiente linear!';
            document.getElementById('dots-type').value = 'rounded';
            document.getElementById('dots-use-gradient').checked = true;
            toggleGradientControls('dots');
            break;

          case 'corporate':
            document.getElementById('qr-data').value =
              'https://minhaempresa.com';
            document.getElementById('dots-type').value = 'classy-rounded';
            document.getElementById('dots-color').value = '#2c3e50';

            document.getElementById('corners-square-enabled').checked = true;
            document.getElementById('corners-square-type').value =
              'extra-rounded';
            document.getElementById('corners-square-color').value = '#e74c3c';
            toggleCornerOptions('corners-square');

            document.getElementById('corners-dot-enabled').checked = true;
            document.getElementById('corners-dot-type').value = 'dot';
            document.getElementById('corners-dot-color').value = '#3498db';
            toggleCornerOptions('corners-dot');

            document.getElementById('background-color').value = '#ecf0f1';
            document.getElementById('background-round').value = '0.1';
            break;

          case 'modern':
            document.getElementById('qr-data').value =
              'Design moderno e elegante';
            document.getElementById('dots-type').value = 'extra-rounded';
            document.getElementById('dots-use-gradient').checked = true;
            document.getElementById('dots-gradient-type').value = 'radial';
            toggleGradientControls('dots');

            // Configurar gradiente radial
            const dotsColorStops = document.getElementById('dots-color-stops');
            dotsColorStops.querySelector(
              '.color-stop:first-child input[type="color"]'
            ).value = '#667eea';
            dotsColorStops.querySelector(
              '.color-stop:last-child input[type="color"]'
            ).value = '#764ba2';
            break;

          case 'colorful':
            document.getElementById('qr-data').value =
              'QR Code colorido e vibrante!';
            document.getElementById('dots-type').value = 'dots';
            document.getElementById('dots-use-gradient').checked = true;
            document.getElementById('dots-gradient-rotation').value = '45';
            toggleGradientControls('dots');

            // Adicionar mais cores
            addColorStop('dots');
            const colorStops = document
              .getElementById('dots-color-stops')
              .querySelectorAll('.color-stop');
            colorStops[0].querySelector('input[type="color"]').value =
              '#ff6b6b';
            colorStops[1].querySelector('input[type="color"]').value =
              '#4ecdc4';
            colorStops[2].querySelector('input[type="color"]').value =
              '#45b7d1';
            colorStops[1].querySelector('input[type="range"]').value = '0.5';
            colorStops[1].querySelector('.range-value').textContent = '50%';
            break;

          case 'minimal':
            document.getElementById('qr-data').value =
              'Minimalismo é elegância';
            document.getElementById('dots-type').value = 'rounded';
            document.getElementById('dots-color').value = '#2c3e50';
            document.getElementById('background-color').value = '#ffffff';
            document.getElementById('qr-margin').value = '20';
            break;
        }

        updateAllRangeValues();
        updateQRCode();
      };

      // Função para resetar controles
      function resetControls() {
        // Resetar checkboxes
        document.getElementById('dots-use-gradient').checked = false;
        document.getElementById('corners-square-enabled').checked = false;
        document.getElementById('corners-dot-enabled').checked = false;
        document.getElementById('background-use-gradient').checked = false;
        document.getElementById('image-enabled').checked = false;

        // Ocultar seções opcionais
        toggleGradientControls('dots');
        toggleGradientControls('corners-square');
        toggleGradientControls('corners-dot');
        toggleGradientControls('background');
        toggleCornerOptions('corners-square');
        toggleCornerOptions('corners-dot');
        toggleImageOptions();

        // Resetar valores padrão
        document.getElementById('qr-width').value = '300';
        document.getElementById('qr-height').value = '300';
        document.getElementById('qr-margin').value = '0';
        document.getElementById('background-round').value = '0';
      }

      // Função para alternar controles de gradiente
      function toggleGradientControls(prefix) {
        const useGradient = document.getElementById(
          `${prefix}-use-gradient`
        ).checked;
        const gradientControls = document.getElementById(
          `${prefix}-gradient-controls`
        );
        const solidColor = document.getElementById(`${prefix}-solid-color`);
        const rotationGroup = document.getElementById(
          `${prefix}-gradient-rotation-group`
        );

        if (gradientControls) {
          gradientControls.classList.toggle('active', useGradient);
        }

        if (solidColor) {
          solidColor.style.display = useGradient ? 'none' : 'block';
        }

        // Mostrar/ocultar rotação baseado no tipo de gradiente
        if (rotationGroup) {
          const gradientType = document.getElementById(
            `${prefix}-gradient-type`
          ).value;
          rotationGroup.style.display =
            useGradient && gradientType === 'linear' ? 'block' : 'none';
        }
      }

      // Função para alternar opções de cantos
      function toggleCornerOptions(prefix) {
        const enabled = document.getElementById(`${prefix}-enabled`).checked;
        const options = document.getElementById(`${prefix}-options`);

        if (options) {
          options.style.display = enabled ? 'block' : 'none';
        }
      }

      // Função para alternar opções de imagem
      function toggleImageOptions() {
        const enabled = document.getElementById('image-enabled').checked;
        const options = document.getElementById('image-options');

        if (options) {
          options.style.display = enabled ? 'block' : 'none';
        }
      }

      // Função para atualizar todos os valores de range
      function updateAllRangeValues() {
        updateRangeValue('qr-width', 'width-value', 'px');
        updateRangeValue('qr-height', 'height-value', 'px');
        updateRangeValue('qr-margin', 'margin-value', 'px');
        updateRangeValue('type-number', 'type-number-value');
        updateRangeValue('dots-gradient-rotation', 'dots-rotation-value', '°');
        updateRangeValue(
          'corners-square-gradient-rotation',
          'corners-square-rotation-value',
          '°'
        );
        updateRangeValue(
          'corners-dot-gradient-rotation',
          'corners-dot-rotation-value',
          '°'
        );
        updateRangeValue(
          'background-gradient-rotation',
          'background-rotation-value',
          '°'
        );
        updateRangeValue('background-round', 'background-round-value');
        updateRangeValue('image-size', 'image-size-value');
        updateRangeValue('image-margin', 'image-margin-value', 'px');

        // Atualizar valores de paradas de cor
        document
          .querySelectorAll('.color-stop input[type="range"]')
          .forEach((range) => {
            const valueSpan = range.nextElementSibling;
            if (valueSpan && valueSpan.classList.contains('range-value')) {
              valueSpan.textContent =
                Math.round(parseFloat(range.value) * 100) + '%';
            }
          });
      }

      // Event listeners
      function setupEventListeners() {
        // Inputs básicos
        document
          .getElementById('qr-data')
          .addEventListener('input', updateQRCode);
        document
          .getElementById('qr-type')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('qr-shape')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('qr-width')
          .addEventListener('input', updateQRCode);
        document
          .getElementById('qr-height')
          .addEventListener('input', updateQRCode);
        document
          .getElementById('qr-margin')
          .addEventListener('input', updateQRCode);

        // Configurações QR
        document
          .getElementById('error-correction')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('qr-mode')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('type-number')
          .addEventListener('input', updateQRCode);

        // Pontos
        document
          .getElementById('dots-type')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('dots-round-size')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('dots-color')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('dots-use-gradient')
          .addEventListener('change', () => {
            toggleGradientControls('dots');
            updateQRCode();
          });

        // Gradiente dos pontos
        document
          .getElementById('dots-gradient-type')
          .addEventListener('change', () => {
            toggleGradientControls('dots');
            updateQRCode();
          });
        document
          .getElementById('dots-gradient-rotation')
          .addEventListener('input', updateQRCode);

        // Cantos quadrados
        document
          .getElementById('corners-square-enabled')
          .addEventListener('change', () => {
            toggleCornerOptions('corners-square');
            updateQRCode();
          });
        document
          .getElementById('corners-square-type')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('corners-square-color')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('corners-square-use-gradient')
          .addEventListener('change', () => {
            toggleGradientControls('corners-square');
            updateQRCode();
          });
        document
          .getElementById('corners-square-gradient-type')
          .addEventListener('change', () => {
            toggleGradientControls('corners-square');
            updateQRCode();
          });
        document
          .getElementById('corners-square-gradient-rotation')
          .addEventListener('input', updateQRCode);

        // Pontos dos cantos
        document
          .getElementById('corners-dot-enabled')
          .addEventListener('change', () => {
            toggleCornerOptions('corners-dot');
            updateQRCode();
          });
        document
          .getElementById('corners-dot-type')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('corners-dot-color')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('corners-dot-use-gradient')
          .addEventListener('change', () => {
            toggleGradientControls('corners-dot');
            updateQRCode();
          });
        document
          .getElementById('corners-dot-gradient-type')
          .addEventListener('change', () => {
            toggleGradientControls('corners-dot');
            updateQRCode();
          });
        document
          .getElementById('corners-dot-gradient-rotation')
          .addEventListener('input', updateQRCode);

        // Fundo
        document
          .getElementById('background-color')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('background-round')
          .addEventListener('input', updateQRCode);
        document
          .getElementById('background-use-gradient')
          .addEventListener('change', () => {
            toggleGradientControls('background');
            updateQRCode();
          });
        document
          .getElementById('background-gradient-type')
          .addEventListener('change', () => {
            toggleGradientControls('background');
            updateQRCode();
          });
        document
          .getElementById('background-gradient-rotation')
          .addEventListener('input', updateQRCode);

        // Imagem
        document
          .getElementById('image-enabled')
          .addEventListener('change', () => {
            toggleImageOptions();
            updateQRCode();
          });
        document
          .getElementById('image-url')
          .addEventListener('input', updateQRCode);
        document
          .getElementById('image-size')
          .addEventListener('input', updateQRCode);
        document
          .getElementById('image-margin')
          .addEventListener('input', updateQRCode);
        document
          .getElementById('image-hide-background')
          .addEventListener('change', updateQRCode);
        document
          .getElementById('image-cross-origin')
          .addEventListener('change', updateQRCode);

        // Event listeners para paradas de cor iniciais
        document.querySelectorAll('.color-stop input').forEach((input) => {
          input.addEventListener('input', updateQRCode);
          input.addEventListener('change', updateQRCode);
        });
      }

      // Inicialização
      async function init() {
        try {
          showStatus('Carregando biblioteca QR Code Styling...', 'loading');

          // Configurar event listeners
          setupEventListeners();

          // Atualizar valores de range
          updateAllRangeValues();

          // Gerar QR Code inicial
          await new Promise((resolve) => setTimeout(resolve, 100));
          updateQRCode();

          showStatus('Biblioteca carregada com sucesso!', 'success');
        } catch (error) {
          showError(error);
        }
      }

      // Inicializar quando a página carregar
      init();
    </script>
  </body>
</html>
