# QR Library Standalone - API Reference

## Classe Principal: QRCodeStyling

### Constructor

```javascript
new QRCodeStyling(options)
```

**Parâmetros:**
- `options` (Object): Objeto de configuração do QR Code

### Métodos

#### `update(options)`
Atualiza as opções do QR Code e re-renderiza.

**Parâmetros:**
- `options` (Object): Novas opções para atualizar

**Exemplo:**
```javascript
qrCode.update({
  data: "https://novo-link.com",
  dotsOptions: {
    color: "#ff0000"
  }
});
```

#### `append(container)`
Anexa o QR Code a um elemento DOM.

**Parâmetros:**
- `container` (HTMLElement): Elemento DOM onde anexar o QR Code

**Exemplo:**
```javascript
qrCode.append(document.getElementById("qr-container"));
```

#### `download(options)`
Faz download do QR Code como arquivo.

**Parâmetros:**
- `options` (Object|String): Opções de download ou extensão do arquivo

**Opções de Download:**
- `name` (String): Nome do arquivo (padrão: "qr")
- `extension` (String): Extensão do arquivo ("png", "jpeg", "svg")

**Exemplos:**
```javascript
// Download como PNG
qrCode.download({ name: "meu-qr", extension: "png" });

// Download como SVG
qrCode.download("svg");
```

## Opções de Configuração

### Opções Principais

```javascript
{
  type: "canvas" | "svg",           // Tipo de renderização
  shape: "square" | "circle",       // Forma do QR Code
  width: 300,                       // Largura em pixels
  height: 300,                      // Altura em pixels
  data: "",                         // Dados do QR Code
  margin: 0,                        // Margem ao redor do QR Code
  image: "url-da-imagem"            // URL da imagem/logo central
}
```

### qrOptions

```javascript
qrOptions: {
  typeNumber: 0,                    // Versão do QR Code (0 = automático, 1-40)
  mode: "Byte",                     // Modo de codificação
  errorCorrectionLevel: "Q"         // Nível de correção de erro
}
```

**Modos de Codificação:**
- `"Numeric"`: Apenas números
- `"Alphanumeric"`: Números, letras maiúsculas e alguns símbolos
- `"Byte"`: Qualquer caractere (padrão)
- `"Kanji"`: Caracteres Kanji

**Níveis de Correção de Erro:**
- `"L"`: ~7% de recuperação
- `"M"`: ~15% de recuperação
- `"Q"`: ~25% de recuperação (padrão)
- `"H"`: ~30% de recuperação

### dotsOptions

```javascript
dotsOptions: {
  type: "square",                   // Tipo dos pontos
  color: "#000000",                 // Cor sólida
  gradient: {                       // Gradiente (opcional)
    type: "linear",                 // "linear" ou "radial"
    rotation: 0,                    // Rotação em radianos
    colorStops: [                   // Paradas de cor
      { offset: 0, color: "#000" },
      { offset: 1, color: "#fff" }
    ]
  },
  roundSize: true                   // Arredondar tamanho dos pontos
}
```

**Tipos de Pontos:**
- `"square"`: Quadrados (padrão)
- `"dots"`: Círculos
- `"rounded"`: Quadrados com bordas arredondadas
- `"extra-rounded"`: Bordas extra arredondadas
- `"classy"`: Estilo elegante
- `"classy-rounded"`: Estilo elegante arredondado

### cornersSquareOptions

```javascript
cornersSquareOptions: {
  type: "square",                   // Tipo dos cantos quadrados
  color: "#000000",                 // Cor sólida
  gradient: {                       // Gradiente (opcional)
    type: "linear",
    rotation: 0,
    colorStops: [...]
  }
}
```

**Tipos de Cantos Quadrados:**
- `"square"`: Quadrado (padrão)
- `"dot"`: Circular
- `"extra-rounded"`: Extra arredondado

### cornersDotOptions

```javascript
cornersDotOptions: {
  type: "square",                   // Tipo dos pontos dos cantos
  color: "#000000",                 // Cor sólida
  gradient: {                       // Gradiente (opcional)
    type: "linear",
    rotation: 0,
    colorStops: [...]
  }
}
```

**Tipos de Pontos dos Cantos:**
- `"square"`: Quadrado (padrão)
- `"dot"`: Circular

### backgroundOptions

```javascript
backgroundOptions: {
  color: "#ffffff",                 // Cor de fundo
  gradient: {                       // Gradiente de fundo (opcional)
    type: "linear",
    rotation: 0,
    colorStops: [...]
  },
  round: 0                          // Arredondamento das bordas (0-1)
}
```

### imageOptions

```javascript
imageOptions: {
  hideBackgroundDots: true,         // Ocultar pontos atrás da imagem
  imageSize: 0.4,                   // Tamanho da imagem (0-1)
  crossOrigin: "anonymous",         // CORS para imagens
  margin: 0                         // Margem da imagem
}
```

## Constantes Exportadas

```javascript
import QRCodeStyling, {
  dotTypes,
  cornerDotTypes,
  cornerSquareTypes,
  errorCorrectionLevels,
  modes,
  gradientTypes,
  drawTypes,
  shapeTypes
} from './qr-library-standalone/index.js';
```

### dotTypes
```javascript
{
  square: "square",
  dots: "dots",
  rounded: "rounded",
  extraRounded: "extra-rounded",
  classy: "classy",
  classyRounded: "classy-rounded"
}
```

### cornerDotTypes
```javascript
{
  square: "square",
  dot: "dot"
}
```

### cornerSquareTypes
```javascript
{
  square: "square",
  dot: "dot",
  extraRounded: "extra-rounded"
}
```

### errorCorrectionLevels
```javascript
{
  L: "L",
  M: "M",
  Q: "Q",
  H: "H"
}
```

### modes
```javascript
{
  Numeric: "Numeric",
  Alphanumeric: "Alphanumeric",
  Byte: "Byte",
  Kanji: "Kanji"
}
```

### gradientTypes
```javascript
{
  linear: "linear",
  radial: "radial"
}
```

### drawTypes
```javascript
{
  canvas: "canvas",
  svg: "svg"
}
```

### shapeTypes
```javascript
{
  square: "square",
  circle: "circle"
}
```

## Exemplos Avançados

### QR Code com Gradiente Linear
```javascript
const qrCode = new QRCodeStyling({
  width: 300,
  height: 300,
  data: "https://exemplo.com",
  dotsOptions: {
    type: "rounded",
    gradient: {
      type: "linear",
      rotation: Math.PI / 4,
      colorStops: [
        { offset: 0, color: "#ff0000" },
        { offset: 0.5, color: "#00ff00" },
        { offset: 1, color: "#0000ff" }
      ]
    }
  }
});
```

### QR Code com Gradiente Radial
```javascript
const qrCode = new QRCodeStyling({
  width: 300,
  height: 300,
  data: "https://exemplo.com",
  dotsOptions: {
    type: "dots",
    gradient: {
      type: "radial",
      colorStops: [
        { offset: 0, color: "#ffffff" },
        { offset: 1, color: "#000000" }
      ]
    }
  }
});
```

### QR Code com Logo e Cantos Personalizados
```javascript
const qrCode = new QRCodeStyling({
  width: 300,
  height: 300,
  data: "https://exemplo.com",
  image: "https://exemplo.com/logo.png",
  dotsOptions: {
    type: "classy-rounded",
    color: "#4267b2"
  },
  cornersSquareOptions: {
    type: "extra-rounded",
    color: "#ff6b6b"
  },
  cornersDotOptions: {
    type: "dot",
    color: "#4ecdc4"
  },
  backgroundOptions: {
    color: "#f7f7f7",
    round: 0.1
  },
  imageOptions: {
    hideBackgroundDots: true,
    imageSize: 0.3,
    margin: 5
  }
});
```
