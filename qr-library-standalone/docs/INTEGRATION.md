# Guia de Integração - QR Library Standalone

## Instalação

### 1. Download dos Arquivos

Copie a pasta `qr-library-standalone` para o seu projeto:

```bash
cp -r qr-library-standalone/ seu-projeto/libs/
```

### 2. Estrutura de Arquivos

```
qr-library-standalone/
├── index.js              # Arquivo principal
├── qr-core.js            # Utilitários e constantes
├── qr-generator.js       # Gerador de QR Code
├── qr-styling.js         # Estilização e renderização
├── README.md             # Documentação básica
├── docs/
│   ├── API.md           # Referência da API
│   └── INTEGRATION.md   # Este guia
└── examples/
    └── basic.html       # Exemplos práticos
```

## Integração em Diferentes Ambientes

### 1. Mó<PERSON>los ES6 (Recomendado)

```html
<!DOCTYPE html>
<html>
<head>
    <title>Meu App com QR Code</title>
</head>
<body>
    <div id="qr-container"></div>
    
    <script type="module">
        import QRCodeStyling from './libs/qr-library-standalone/index.js';
        
        const qrCode = new QRCodeStyling({
            width: 300,
            height: 300,
            data: "https://meusite.com",
            dotsOptions: {
                type: "rounded",
                color: "#4267b2"
            }
        });
        
        qrCode.append(document.getElementById('qr-container'));
    </script>
</body>
</html>
```

### 2. Webpack/Bundlers

```javascript
// main.js
import QRCodeStyling from './libs/qr-library-standalone/index.js';

const qrCode = new QRCodeStyling({
    width: 300,
    height: 300,
    data: "https://exemplo.com"
});

qrCode.append(document.getElementById('qr-container'));
```

### 3. Node.js (com jsdom)

```javascript
// server.js
import { JSDOM } from 'jsdom';
import QRCodeStyling from './libs/qr-library-standalone/index.js';

const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
global.window = dom.window;
global.document = dom.window.document;

const qrCode = new QRCodeStyling({
    width: 300,
    height: 300,
    data: "https://exemplo.com",
    type: "svg"
});

// Gerar SVG
const svgElement = qrCode.getElement();
const svgString = new dom.window.XMLSerializer().serializeToString(svgElement);
console.log(svgString);
```

### 4. React

```jsx
// QRComponent.jsx
import React, { useEffect, useRef } from 'react';
import QRCodeStyling from './libs/qr-library-standalone/index.js';

const QRComponent = ({ data, options = {} }) => {
    const ref = useRef(null);
    const qrCode = useRef(null);
    
    useEffect(() => {
        if (!qrCode.current) {
            qrCode.current = new QRCodeStyling({
                width: 300,
                height: 300,
                data: data,
                ...options
            });
        }
        
        if (ref.current) {
            qrCode.current.append(ref.current);
        }
    }, []);
    
    useEffect(() => {
        if (qrCode.current) {
            qrCode.current.update({ data, ...options });
        }
    }, [data, options]);
    
    return <div ref={ref} />;
};

export default QRComponent;
```

### 5. Vue.js

```vue
<!-- QRComponent.vue -->
<template>
    <div ref="qrContainer"></div>
</template>

<script>
import QRCodeStyling from './libs/qr-library-standalone/index.js';

export default {
    name: 'QRComponent',
    props: {
        data: {
            type: String,
            required: true
        },
        options: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            qrCode: null
        };
    },
    mounted() {
        this.qrCode = new QRCodeStyling({
            width: 300,
            height: 300,
            data: this.data,
            ...this.options
        });
        
        this.qrCode.append(this.$refs.qrContainer);
    },
    watch: {
        data: {
            handler(newData) {
                if (this.qrCode) {
                    this.qrCode.update({ data: newData });
                }
            }
        },
        options: {
            handler(newOptions) {
                if (this.qrCode) {
                    this.qrCode.update(newOptions);
                }
            },
            deep: true
        }
    }
};
</script>
```

### 6. Angular

```typescript
// qr-component.component.ts
import { Component, Input, ElementRef, ViewChild, OnInit, OnChanges } from '@angular/core';
import QRCodeStyling from './libs/qr-library-standalone/index.js';

@Component({
    selector: 'app-qr-component',
    template: '<div #qrContainer></div>'
})
export class QRComponent implements OnInit, OnChanges {
    @Input() data: string = '';
    @Input() options: any = {};
    @ViewChild('qrContainer', { static: true }) qrContainer!: ElementRef;
    
    private qrCode: any = null;
    
    ngOnInit() {
        this.qrCode = new QRCodeStyling({
            width: 300,
            height: 300,
            data: this.data,
            ...this.options
        });
        
        this.qrCode.append(this.qrContainer.nativeElement);
    }
    
    ngOnChanges() {
        if (this.qrCode) {
            this.qrCode.update({
                data: this.data,
                ...this.options
            });
        }
    }
}
```

## Configurações Avançadas

### 1. QR Code Responsivo

```javascript
function createResponsiveQR(container, data) {
    const size = Math.min(container.offsetWidth, container.offsetHeight);
    
    const qrCode = new QRCodeStyling({
        width: size,
        height: size,
        data: data,
        dotsOptions: {
            type: "rounded",
            color: "#000"
        }
    });
    
    qrCode.append(container);
    
    // Redimensionar quando a janela mudar
    window.addEventListener('resize', () => {
        const newSize = Math.min(container.offsetWidth, container.offsetHeight);
        qrCode.update({ width: newSize, height: newSize });
    });
    
    return qrCode;
}
```

### 2. QR Code com Tema Escuro/Claro

```javascript
function createThemedQR(data, isDark = false) {
    const theme = isDark ? {
        dotsOptions: { color: "#ffffff" },
        backgroundOptions: { color: "#000000" },
        cornersSquareOptions: { color: "#ffffff" },
        cornersDotOptions: { color: "#ffffff" }
    } : {
        dotsOptions: { color: "#000000" },
        backgroundOptions: { color: "#ffffff" },
        cornersSquareOptions: { color: "#000000" },
        cornersDotOptions: { color: "#000000" }
    };
    
    return new QRCodeStyling({
        width: 300,
        height: 300,
        data: data,
        ...theme
    });
}
```

### 3. QR Code com Validação

```javascript
function createValidatedQR(data, options = {}) {
    // Validar dados
    if (!data || data.trim() === '') {
        throw new Error('Dados do QR Code não podem estar vazios');
    }
    
    // Validar URL se aplicável
    if (data.startsWith('http')) {
        try {
            new URL(data);
        } catch {
            console.warn('URL pode não ser válida:', data);
        }
    }
    
    // Validar tamanho dos dados
    if (data.length > 2953) {
        console.warn('Dados muito longos, podem não ser legíveis em QR Codes pequenos');
    }
    
    return new QRCodeStyling({
        width: 300,
        height: 300,
        data: data,
        qrOptions: {
            errorCorrectionLevel: data.length > 1000 ? 'L' : 'Q'
        },
        ...options
    });
}
```

## Otimizações de Performance

### 1. Lazy Loading

```javascript
// Carregar biblioteca apenas quando necessário
async function loadQRLibrary() {
    const { default: QRCodeStyling } = await import('./libs/qr-library-standalone/index.js');
    return QRCodeStyling;
}

// Usar quando necessário
document.getElementById('generate-qr').addEventListener('click', async () => {
    const QRCodeStyling = await loadQRLibrary();
    const qrCode = new QRCodeStyling({
        width: 300,
        height: 300,
        data: "https://exemplo.com"
    });
    qrCode.append(document.getElementById('qr-container'));
});
```

### 2. Reutilização de Instâncias

```javascript
class QRManager {
    constructor() {
        this.qrCode = null;
        this.container = null;
    }
    
    init(container) {
        this.container = container;
        this.qrCode = new QRCodeStyling({
            width: 300,
            height: 300,
            data: ""
        });
        this.qrCode.append(container);
    }
    
    updateData(data) {
        if (this.qrCode) {
            this.qrCode.update({ data });
        }
    }
    
    updateStyle(options) {
        if (this.qrCode) {
            this.qrCode.update(options);
        }
    }
}
```

## Tratamento de Erros

```javascript
function createQRWithErrorHandling(data, options = {}) {
    try {
        const qrCode = new QRCodeStyling({
            width: 300,
            height: 300,
            data: data,
            ...options
        });
        
        return qrCode;
    } catch (error) {
        console.error('Erro ao criar QR Code:', error);
        
        // Fallback para QR Code básico
        return new QRCodeStyling({
            width: 300,
            height: 300,
            data: data || "Erro na geração",
            dotsOptions: { type: "square", color: "#000000" },
            backgroundOptions: { color: "#ffffff" }
        });
    }
}
```

## Compatibilidade

### Navegadores Suportados
- Chrome 61+
- Firefox 60+
- Safari 10.1+
- Edge 16+

### Funcionalidades por Navegador
- **SVG**: Todos os navegadores suportados
- **Canvas**: Todos os navegadores suportados
- **Download**: Todos os navegadores suportados
- **Gradientes**: Todos os navegadores suportados

### Polyfills Necessários
Para navegadores mais antigos, pode ser necessário incluir:
- `URL` polyfill
- `Promise` polyfill
- `fetch` polyfill (se usado para carregar imagens)

## Troubleshooting

### Problemas Comuns

1. **QR Code não aparece**
   - Verifique se o container existe no DOM
   - Verifique se os dados não estão vazios
   - Verifique o console para erros

2. **Imagem não carrega**
   - Verifique CORS da imagem
   - Use `crossOrigin: "anonymous"` nas imageOptions
   - Teste com imagem local ou data URL

3. **Download não funciona**
   - Verifique se o navegador suporta download
   - Teste com diferentes formatos (PNG, SVG)
   - Verifique se há bloqueadores de popup

4. **Performance lenta**
   - Reduza o tamanho do QR Code
   - Use `roundSize: false` para melhor performance
   - Evite gradientes complexos em QR Codes grandes
