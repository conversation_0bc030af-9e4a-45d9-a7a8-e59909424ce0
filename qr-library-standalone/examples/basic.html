<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Library Standalone - Exemplo Básico</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #555;
            border-bottom: 2px solid #4267b2;
            padding-bottom: 10px;
        }
        
        .qr-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
        }
        
        label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #555;
        }
        
        input, select, button {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: #4267b2;
            color: white;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        
        button:hover {
            background: #365899;
        }
        
        .download-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .example-section {
            margin-bottom: 40px;
        }
        
        pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border-left: 4px solid #4267b2;
        }
        
        code {
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <h1>QR Library Standalone - Exemplos</h1>
    
    <div class="container">
        <h2>Exemplo 1: QR Code Básico</h2>
        <div class="controls">
            <div class="control-group">
                <label for="data1">Dados do QR Code:</label>
                <input type="text" id="data1" value="https://github.com" placeholder="Digite o texto ou URL">
            </div>
            <div class="control-group">
                <label for="color1">Cor dos Pontos:</label>
                <input type="color" id="color1" value="#000000">
            </div>
            <div class="control-group">
                <label for="bgColor1">Cor de Fundo:</label>
                <input type="color" id="bgColor1" value="#ffffff">
            </div>
            <div class="control-group">
                <label for="dotType1">Tipo dos Pontos:</label>
                <select id="dotType1">
                    <option value="square">Quadrado</option>
                    <option value="dots">Círculos</option>
                    <option value="rounded">Arredondado</option>
                    <option value="extra-rounded">Extra Arredondado</option>
                    <option value="classy">Elegante</option>
                    <option value="classy-rounded">Elegante Arredondado</option>
                </select>
            </div>
        </div>
        <button onclick="updateBasicQR()">Atualizar QR Code</button>
        <div class="qr-container" id="qr-basic"></div>
        <div class="download-buttons">
            <button onclick="downloadQR('basic', 'png')">Download PNG</button>
            <button onclick="downloadQR('basic', 'svg')">Download SVG</button>
        </div>
    </div>

    <div class="container">
        <h2>Exemplo 2: QR Code com Gradiente</h2>
        <div class="controls">
            <div class="control-group">
                <label for="data2">Dados do QR Code:</label>
                <input type="text" id="data2" value="QR Code com gradiente!" placeholder="Digite o texto">
            </div>
            <div class="control-group">
                <label for="gradientType">Tipo de Gradiente:</label>
                <select id="gradientType">
                    <option value="linear">Linear</option>
                    <option value="radial">Radial</option>
                </select>
            </div>
            <div class="control-group">
                <label for="color2a">Cor Inicial:</label>
                <input type="color" id="color2a" value="#ff6b6b">
            </div>
            <div class="control-group">
                <label for="color2b">Cor Final:</label>
                <input type="color" id="color2b" value="#4ecdc4">
            </div>
        </div>
        <button onclick="updateGradientQR()">Atualizar QR Code</button>
        <div class="qr-container" id="qr-gradient"></div>
        <div class="download-buttons">
            <button onclick="downloadQR('gradient', 'png')">Download PNG</button>
            <button onclick="downloadQR('gradient', 'svg')">Download SVG</button>
        </div>
    </div>

    <div class="container">
        <h2>Exemplo 3: QR Code com Logo</h2>
        <div class="controls">
            <div class="control-group">
                <label for="data3">Dados do QR Code:</label>
                <input type="text" id="data3" value="https://exemplo.com" placeholder="Digite o texto ou URL">
            </div>
            <div class="control-group">
                <label for="logoUrl">URL do Logo:</label>
                <input type="text" id="logoUrl" value="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM0MjY3YjIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+Cjwvc3ZnPgo=" placeholder="URL da imagem do logo">
            </div>
            <div class="control-group">
                <label for="imageSize">Tamanho do Logo:</label>
                <input type="range" id="imageSize" min="0.1" max="0.6" step="0.1" value="0.4">
                <span id="imageSizeValue">0.4</span>
            </div>
            <div class="control-group">
                <label for="hideBackground">Ocultar Pontos de Fundo:</label>
                <input type="checkbox" id="hideBackground" checked>
            </div>
        </div>
        <button onclick="updateLogoQR()">Atualizar QR Code</button>
        <div class="qr-container" id="qr-logo"></div>
        <div class="download-buttons">
            <button onclick="downloadQR('logo', 'png')">Download PNG</button>
            <button onclick="downloadQR('logo', 'svg')">Download SVG</button>
        </div>
    </div>

    <div class="container">
        <h2>Código de Exemplo</h2>
        <pre><code>// Importar a biblioteca
import QRCodeStyling from './qr-library-standalone/index.js';

// Criar QR Code básico
const qrCode = new QRCodeStyling({
    width: 300,
    height: 300,
    data: "https://exemplo.com",
    dotsOptions: {
        type: "rounded",
        color: "#4267b2"
    },
    backgroundOptions: {
        color: "#ffffff"
    }
});

// Anexar ao DOM
qrCode.append(document.getElementById("container"));

// Download
qrCode.download({ name: "meu-qr", extension: "png" });</code></pre>
    </div>

    <script type="module">
        import QRCodeStyling from '../index.js';
        
        // Variáveis globais para os QR Codes
        window.qrBasic = null;
        window.qrGradient = null;
        window.qrLogo = null;
        
        // Função para criar QR Code básico
        window.updateBasicQR = function() {
            const data = document.getElementById('data1').value;
            const color = document.getElementById('color1').value;
            const bgColor = document.getElementById('bgColor1').value;
            const dotType = document.getElementById('dotType1').value;
            
            const container = document.getElementById('qr-basic');
            container.innerHTML = '';
            
            window.qrBasic = new QRCodeStyling({
                width: 300,
                height: 300,
                data: data,
                dotsOptions: {
                    type: dotType,
                    color: color
                },
                backgroundOptions: {
                    color: bgColor
                }
            });
            
            window.qrBasic.append(container);
        };
        
        // Função para criar QR Code com gradiente
        window.updateGradientQR = function() {
            const data = document.getElementById('data2').value;
            const gradientType = document.getElementById('gradientType').value;
            const colorA = document.getElementById('color2a').value;
            const colorB = document.getElementById('color2b').value;
            
            const container = document.getElementById('qr-gradient');
            container.innerHTML = '';
            
            window.qrGradient = new QRCodeStyling({
                width: 300,
                height: 300,
                data: data,
                dotsOptions: {
                    type: "rounded",
                    gradient: {
                        type: gradientType,
                        rotation: gradientType === "linear" ? Math.PI / 4 : 0,
                        colorStops: [
                            { offset: 0, color: colorA },
                            { offset: 1, color: colorB }
                        ]
                    }
                },
                backgroundOptions: {
                    color: "#ffffff"
                }
            });
            
            window.qrGradient.append(container);
        };
        
        // Função para criar QR Code com logo
        window.updateLogoQR = function() {
            const data = document.getElementById('data3').value;
            const logoUrl = document.getElementById('logoUrl').value;
            const imageSize = parseFloat(document.getElementById('imageSize').value);
            const hideBackground = document.getElementById('hideBackground').checked;
            
            document.getElementById('imageSizeValue').textContent = imageSize;
            
            const container = document.getElementById('qr-logo');
            container.innerHTML = '';
            
            window.qrLogo = new QRCodeStyling({
                width: 300,
                height: 300,
                data: data,
                image: logoUrl,
                dotsOptions: {
                    type: "classy-rounded",
                    color: "#4267b2"
                },
                cornersSquareOptions: {
                    type: "extra-rounded",
                    color: "#ff6b6b"
                },
                cornersDotOptions: {
                    type: "dot",
                    color: "#4ecdc4"
                },
                backgroundOptions: {
                    color: "#ffffff"
                },
                imageOptions: {
                    hideBackgroundDots: hideBackground,
                    imageSize: imageSize,
                    margin: 5
                }
            });
            
            window.qrLogo.append(container);
        };
        
        // Função para download
        window.downloadQR = function(type, extension) {
            let qr = null;
            switch(type) {
                case 'basic':
                    qr = window.qrBasic;
                    break;
                case 'gradient':
                    qr = window.qrGradient;
                    break;
                case 'logo':
                    qr = window.qrLogo;
                    break;
            }
            
            if (qr) {
                qr.download({ name: `qr-${type}`, extension: extension });
            }
        };
        
        // Atualizar slider de tamanho da imagem
        document.getElementById('imageSize').addEventListener('input', function() {
            document.getElementById('imageSizeValue').textContent = this.value;
        });
        
        // Inicializar QR Codes
        window.updateBasicQR();
        window.updateGradientQR();
        window.updateLogoQR();
    </script>
</body>
</html>
