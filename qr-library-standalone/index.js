/**
 * QR Library Standalone - Main Module
 * Biblioteca JavaScript standalone para geração e estilização de QR Codes
 * Versão unificada sem dependências externas
 */

import { QRMode, QRErrorCorrectionLevel, QRMaskPattern, QRUtil, QRMath } from './qr-core.js';
import { QRCodeGenerator } from './qr-generator.js';
import { QRStyling } from './qr-styling.js';

// Tipos de pontos disponíveis
const dotTypes = {
  square: "square",
  dots: "dots", 
  rounded: "rounded",
  extraRounded: "extra-rounded",
  classy: "classy",
  classyRounded: "classy-rounded"
};

// Tipos de cantos disponíveis
const cornerDotTypes = {
  square: "square",
  dot: "dot"
};

const cornerSquareTypes = {
  square: "square",
  dot: "dot", 
  extraRounded: "extra-rounded"
};

// Níveis de correção de erro
const errorCorrectionLevels = {
  L: "L",
  M: "M", 
  Q: "Q",
  H: "H"
};

// Modos de codificação
const modes = {
  Numeric: "Numeric",
  Alphanumeric: "Alphanumeric",
  Byte: "Byte",
  Kanji: "Kanji"
};

// Tipos de gradiente
const gradientTypes = {
  linear: "linear",
  radial: "radial"
};

// Tipos de desenho
const drawTypes = {
  canvas: "canvas",
  svg: "svg"
};

// Tipos de forma
const shapeTypes = {
  square: "square",
  circle: "circle"
};

// Opções padrão
const defaultOptions = {
  type: drawTypes.canvas,
  shape: shapeTypes.square,
  width: 300,
  height: 300,
  data: "",
  margin: 0,
  qrOptions: {
    typeNumber: 0,
    mode: undefined,
    errorCorrectionLevel: errorCorrectionLevels.Q
  },
  imageOptions: {
    saveAsBlob: true,
    hideBackgroundDots: true,
    imageSize: 0.4,
    crossOrigin: undefined,
    margin: 0
  },
  dotsOptions: {
    type: dotTypes.square,
    color: "#000000",
    roundSize: true
  },
  cornersSquareOptions: {
    type: undefined,
    color: undefined
  },
  cornersDotOptions: {
    type: undefined,
    color: undefined
  },
  backgroundOptions: {
    round: 0,
    color: "#ffffff"
  }
};

// Função para mesclar opções
function mergeOptions(target, source) {
  if (!source || typeof source !== 'object') return target;
  
  const result = { ...target };
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = mergeOptions(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
  }
  
  return result;
}

// Função para validar gradiente
function validateGradient(gradient) {
  if (!gradient || !gradient.colorStops || !gradient.colorStops.length) {
    throw new Error("Field 'colorStops' is required in gradient");
  }
  
  const result = { ...gradient };
  result.rotation = Number(result.rotation) || 0;
  result.colorStops = result.colorStops.map(stop => ({
    ...stop,
    offset: Number(stop.offset)
  }));
  
  return result;
}

// Função para sanitizar opções
function sanitizeOptions(options) {
  const result = { ...options };
  
  result.width = Number(result.width);
  result.height = Number(result.height);
  result.margin = Number(result.margin);
  
  if (result.margin > Math.min(result.width, result.height)) {
    result.margin = Math.min(result.width, result.height);
  }
  
  if (result.imageOptions) {
    result.imageOptions = {
      ...result.imageOptions,
      hideBackgroundDots: Boolean(result.imageOptions.hideBackgroundDots),
      imageSize: Number(result.imageOptions.imageSize),
      margin: Number(result.imageOptions.margin)
    };
  }
  
  if (result.dotsOptions && result.dotsOptions.gradient) {
    result.dotsOptions.gradient = validateGradient(result.dotsOptions.gradient);
  }
  
  if (result.cornersSquareOptions && result.cornersSquareOptions.gradient) {
    result.cornersSquareOptions.gradient = validateGradient(result.cornersSquareOptions.gradient);
  }
  
  if (result.cornersDotOptions && result.cornersDotOptions.gradient) {
    result.cornersDotOptions.gradient = validateGradient(result.cornersDotOptions.gradient);
  }
  
  if (result.backgroundOptions && result.backgroundOptions.gradient) {
    result.backgroundOptions.gradient = validateGradient(result.backgroundOptions.gradient);
  }
  
  return result;
}

// Função para detectar modo automaticamente
function getMode(data) {
  if (/^[0-9]*$/.test(data)) {
    return modes.Numeric;
  } else if (/^[0-9A-Z $%*+\-./:]*$/.test(data)) {
    return modes.Alphanumeric;
  } else {
    return modes.Byte;
  }
}

// Classe principal QRCodeStyling
class QRCodeStyling {
  constructor(options = {}) {
    this._options = sanitizeOptions(mergeOptions(defaultOptions, options));
    this._container = null;
    this._domCanvas = null;
    this._svg = null;
    this._qr = null;
    this._canvasDrawingPromise = null;
    this._svgDrawingPromise = null;
    
    this.update();
  }
  
  static _clearContainer(container) {
    if (container) {
      container.innerHTML = "";
    }
  }
  
  update(options) {
    QRCodeStyling._clearContainer(this._container);
    
    if (options) {
      this._options = sanitizeOptions(mergeOptions(this._options, options));
    }
    
    if (this._options.data) {
      // Criar instância do gerador de QR Code
      this._qr = new QRCodeGenerator(
        this._options.qrOptions.typeNumber,
        this._options.qrOptions.errorCorrectionLevel
      );
      
      // Adicionar dados
      this._qr.addData(
        this._options.data,
        this._options.qrOptions.mode || getMode(this._options.data)
      );
      
      // Gerar QR Code
      this._qr.make();
      
      // Configurar renderização
      if (this._options.type === drawTypes.canvas) {
        this._setupCanvas();
      } else {
        this._setupSvg();
      }
      
      this.append(this._container);
    }
  }
  
  _setupCanvas() {
    if (!this._qr) return;
    
    this._domCanvas = document.createElement("canvas");
    this._domCanvas.width = this._options.width;
    this._domCanvas.height = this._options.height;
    
    this._setupSvg();
    this._canvasDrawingPromise = this._svgDrawingPromise?.then(() => {
      if (!this._svg) return;
      
      const svgData = new XMLSerializer().serializeToString(this._svg);
      const svgBlob = new Blob([svgData], { type: "image/svg+xml;charset=utf-8" });
      const url = URL.createObjectURL(svgBlob);
      
      const img = new Image();
      return new Promise((resolve) => {
        img.onload = () => {
          const ctx = this._domCanvas.getContext("2d");
          ctx.drawImage(img, 0, 0);
          URL.revokeObjectURL(url);
          resolve();
        };
        img.src = url;
      });
    });
  }
  
  _setupSvg() {
    if (!this._qr) return;
    
    const styling = new QRStyling(this._options, window);
    this._svg = styling.getElement();
    this._svgDrawingPromise = styling.drawQR(this._qr);
  }
  
  append(container) {
    if (container) {
      if (typeof container.appendChild !== "function") {
        throw new Error("Container should be a single DOM node");
      }
      
      if (this._options.type === drawTypes.canvas && this._domCanvas) {
        container.appendChild(this._domCanvas);
      } else if (this._svg) {
        container.appendChild(this._svg);
      }
      
      this._container = container;
    }
  }
  
  async download(downloadOptions = {}) {
    if (!this._qr) {
      throw new Error("QR code is empty");
    }
    
    let extension = "png";
    let name = "qr";
    
    if (typeof downloadOptions === "string") {
      extension = downloadOptions;
    } else if (typeof downloadOptions === "object" && downloadOptions !== null) {
      if (downloadOptions.name) name = downloadOptions.name;
      if (downloadOptions.extension) extension = downloadOptions.extension;
    }
    
    const element = await this._getElement(extension);
    if (!element) return;
    
    if (extension.toLowerCase() === "svg") {
      const svgData = new XMLSerializer().serializeToString(element);
      const svgBlob = new Blob([svgData], { type: "image/svg+xml;charset=utf-8" });
      const url = URL.createObjectURL(svgBlob);
      this._downloadURI(url, `${name}.svg`);
      URL.revokeObjectURL(url);
    } else {
      const dataURL = element.toDataURL(`image/${extension}`);
      this._downloadURI(dataURL, `${name}.${extension}`);
    }
  }
  
  async _getElement(extension = "png") {
    if (!this._qr) {
      throw new Error("QR code is empty");
    }
    
    if (extension.toLowerCase() === "svg") {
      if (!this._svg && !this._svgDrawingPromise) {
        this._setupSvg();
      }
      await this._svgDrawingPromise;
      return this._svg;
    } else {
      if (!this._domCanvas && !this._canvasDrawingPromise) {
        this._setupCanvas();
      }
      await this._canvasDrawingPromise;
      return this._domCanvas;
    }
  }
  
  _downloadURI(uri, name) {
    const link = document.createElement("a");
    link.download = name;
    link.href = uri;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

// Exportar classe principal e constantes
export default QRCodeStyling;
export {
  dotTypes,
  cornerDotTypes,
  cornerSquareTypes,
  errorCorrectionLevels,
  modes,
  gradientTypes,
  drawTypes,
  shapeTypes
};
