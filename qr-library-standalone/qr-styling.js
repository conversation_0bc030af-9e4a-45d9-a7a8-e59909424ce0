/**
 * QR Library Standalone - Styling Module
 * Módulo de estilização de QR Codes
 */

// Padrões de posição dos cantos (7x7)
const cornerSquarePattern = [
  [1,1,1,1,1,1,1],
  [1,0,0,0,0,0,1],
  [1,0,0,0,0,0,1],
  [1,0,0,0,0,0,1],
  [1,0,0,0,0,0,1],
  [1,0,0,0,0,0,1],
  [1,1,1,1,1,1,1]
];

// Padrão dos pontos dos cantos (3x3)
const cornerDotPattern = [
  [0,0,0,0,0,0,0],
  [0,0,0,0,0,0,0],
  [0,0,1,1,1,0,0],
  [0,0,1,1,1,0,0],
  [0,0,1,1,1,0,0],
  [0,0,0,0,0,0,0],
  [0,0,0,0,0,0,0]
];

// Classe para desenhar pontos
class DotFigure {
  constructor(svg, type, window) {
    this.svg = svg;
    this.type = type;
    this.window = window;
    this.element = null;
  }

  draw(x, y, size, getNeighbor) {
    switch (this.type) {
      case "dots":
        this.drawDot(x, y, size);
        break;
      case "rounded":
        this.drawRounded(x, y, size, getNeighbor);
        break;
      case "extra-rounded":
        this.drawExtraRounded(x, y, size, getNeighbor);
        break;
      case "classy":
        this.drawClassy(x, y, size, getNeighbor);
        break;
      case "classy-rounded":
        this.drawClassyRounded(x, y, size, getNeighbor);
        break;
      default:
        this.drawSquare(x, y, size);
    }
  }

  drawSquare(x, y, size) {
    this.element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "rect");
    this.element.setAttribute("x", String(x));
    this.element.setAttribute("y", String(y));
    this.element.setAttribute("width", String(size));
    this.element.setAttribute("height", String(size));
  }

  drawDot(x, y, size) {
    this.element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "circle");
    this.element.setAttribute("cx", String(x + size / 2));
    this.element.setAttribute("cy", String(y + size / 2));
    this.element.setAttribute("r", String(size / 2));
  }

  drawRounded(x, y, size, getNeighbor) {
    const neighbors = {
      left: getNeighbor ? getNeighbor(-1, 0) : false,
      right: getNeighbor ? getNeighbor(1, 0) : false,
      top: getNeighbor ? getNeighbor(0, -1) : false,
      bottom: getNeighbor ? getNeighbor(0, 1) : false
    };

    const neighborCount = Object.values(neighbors).filter(Boolean).length;

    if (neighborCount === 0) {
      this.drawDot(x, y, size);
    } else if (neighborCount > 2 || (neighbors.left && neighbors.right) || (neighbors.top && neighbors.bottom)) {
      this.drawSquare(x, y, size);
    } else {
      // Desenhar forma arredondada baseada nos vizinhos
      this.element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "path");
      let path = this.createRoundedPath(x, y, size, neighbors);
      this.element.setAttribute("d", path);
    }
  }

  drawExtraRounded(x, y, size, getNeighbor) {
    // Similar ao rounded mas com raios maiores
    this.drawRounded(x, y, size, getNeighbor);
  }

  drawClassy(x, y, size, getNeighbor) {
    const neighbors = {
      left: getNeighbor ? getNeighbor(-1, 0) : false,
      right: getNeighbor ? getNeighbor(1, 0) : false,
      top: getNeighbor ? getNeighbor(0, -1) : false,
      bottom: getNeighbor ? getNeighbor(0, 1) : false
    };

    if (!neighbors.left && !neighbors.top) {
      this.element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "path");
      this.element.setAttribute("d", `M ${x} ${y}v ${size}h ${size}v ${-size/2}a ${size/2} ${size/2}, 0, 0, 0, ${-size/2} ${-size/2}`);
    } else if (!neighbors.right && !neighbors.bottom) {
      this.element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "path");
      this.element.setAttribute("d", `M ${x} ${y}v ${size/2}a ${size/2} ${size/2}, 0, 0, 0, ${size/2} ${size/2}h ${size/2}v ${-size}z`);
    } else {
      this.drawSquare(x, y, size);
    }
  }

  drawClassyRounded(x, y, size, getNeighbor) {
    // Similar ao classy mas com bordas mais arredondadas
    this.drawClassy(x, y, size, getNeighbor);
  }

  createRoundedPath(x, y, size, neighbors) {
    const radius = size / 4;
    let path = `M ${x + radius} ${y}`;

    // Top edge
    if (neighbors.top) {
      path += `h ${size - 2 * radius}`;
    } else {
      path += `h ${size - radius}`;
    }

    // Top-right corner
    if (!neighbors.right && !neighbors.top) {
      path += `a ${radius} ${radius} 0 0 1 ${radius} ${radius}`;
    } else {
      path += `v ${radius}`;
    }

    // Right edge
    if (neighbors.right) {
      path += `v ${size - 2 * radius}`;
    } else {
      path += `v ${size - radius}`;
    }

    // Bottom-right corner
    if (!neighbors.right && !neighbors.bottom) {
      path += `a ${radius} ${radius} 0 0 1 ${-radius} ${radius}`;
    } else {
      path += `h ${-radius}`;
    }

    // Bottom edge
    if (neighbors.bottom) {
      path += `h ${-(size - 2 * radius)}`;
    } else {
      path += `h ${-(size - radius)}`;
    }

    // Bottom-left corner
    if (!neighbors.left && !neighbors.bottom) {
      path += `a ${radius} ${radius} 0 0 1 ${-radius} ${-radius}`;
    } else {
      path += `v ${-radius}`;
    }

    // Left edge
    if (neighbors.left) {
      path += `v ${-(size - 2 * radius)}`;
    } else {
      path += `v ${-(size - radius)}`;
    }

    // Top-left corner
    if (!neighbors.left && !neighbors.top) {
      path += `a ${radius} ${radius} 0 0 1 ${radius} ${-radius}`;
    } else {
      path += `h ${radius}`;
    }

    path += "z";
    return path;
  }
}

// Classe para desenhar cantos quadrados
class CornerSquareFigure {
  constructor(svg, type, window) {
    this.svg = svg;
    this.type = type;
    this.window = window;
    this.element = null;
  }

  draw(x, y, size) {
    switch (this.type) {
      case "dot":
        this.drawDot(x, y, size);
        break;
      case "extra-rounded":
        this.drawExtraRounded(x, y, size);
        break;
      default:
        this.drawSquare(x, y, size);
    }
  }

  drawSquare(x, y, size) {
    const borderWidth = size / 7;
    this.element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "path");
    this.element.setAttribute("clip-rule", "evenodd");
    this.element.setAttribute("d", 
      `M ${x} ${y}v ${size}h ${size}v ${-size}z` +
      `M ${x + borderWidth} ${y + borderWidth}h ${size - 2 * borderWidth}v ${size - 2 * borderWidth}h ${-(size - 2 * borderWidth)}z`
    );
  }

  drawDot(x, y, size) {
    const borderWidth = size / 7;
    this.element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "path");
    this.element.setAttribute("clip-rule", "evenodd");
    this.element.setAttribute("d", 
      `M ${x + size/2} ${y}a ${size/2} ${size/2} 0 1 0 0.1 0zm 0 ${borderWidth}a ${size/2 - borderWidth} ${size/2 - borderWidth} 0 1 1 -0.1 0Z`
    );
  }

  drawExtraRounded(x, y, size) {
    const borderWidth = size / 7;
    const radius = borderWidth * 2.5;
    this.element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "path");
    this.element.setAttribute("clip-rule", "evenodd");
    this.element.setAttribute("d", 
      `M ${x} ${y + radius}v ${size - 2 * radius}a ${radius} ${radius}, 0, 0, 0, ${radius} ${radius}h ${size - 2 * radius}a ${radius} ${radius}, 0, 0, 0, ${radius} ${-radius}v ${-(size - 2 * radius)}a ${radius} ${radius}, 0, 0, 0, ${-radius} ${-radius}h ${-(size - 2 * radius)}a ${radius} ${radius}, 0, 0, 0, ${-radius} ${radius}` +
      `M ${x + radius} ${y + borderWidth}h ${size - 2 * radius}a ${radius - borderWidth} ${radius - borderWidth}, 0, 0, 1, ${radius - borderWidth} ${radius - borderWidth}v ${size - 2 * radius}a ${radius - borderWidth} ${radius - borderWidth}, 0, 0, 1, ${-(radius - borderWidth)} ${radius - borderWidth}h ${-(size - 2 * radius)}a ${radius - borderWidth} ${radius - borderWidth}, 0, 0, 1, ${-(radius - borderWidth)} ${-(radius - borderWidth)}v ${-(size - 2 * radius)}a ${radius - borderWidth} ${radius - borderWidth}, 0, 0, 1, ${radius - borderWidth} ${-(radius - borderWidth)}`
    );
  }
}

// Classe para desenhar pontos dos cantos
class CornerDotFigure {
  constructor(svg, type, window) {
    this.svg = svg;
    this.type = type;
    this.window = window;
    this.element = null;
  }

  draw(x, y, size) {
    if (this.type === "square") {
      this.drawSquare(x, y, size);
    } else {
      this.drawDot(x, y, size);
    }
  }

  drawSquare(x, y, size) {
    this.element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "rect");
    this.element.setAttribute("x", String(x));
    this.element.setAttribute("y", String(y));
    this.element.setAttribute("width", String(size));
    this.element.setAttribute("height", String(size));
  }

  drawDot(x, y, size) {
    this.element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "circle");
    this.element.setAttribute("cx", String(x + size / 2));
    this.element.setAttribute("cy", String(y + size / 2));
    this.element.setAttribute("r", String(size / 2));
  }
}

// Classe principal de estilização
class QRStyling {
  constructor(options, window) {
    this.options = options;
    this.window = window;
    this.instanceId = QRStyling.instanceCount++;
    
    this.element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "svg");
    this.element.setAttribute("width", String(options.width));
    this.element.setAttribute("height", String(options.height));
    this.element.setAttribute("xmlns:xlink", "http://www.w3.org/1999/xlink");
    this.element.setAttribute("viewBox", `0 0 ${options.width} ${options.height}`);
    
    if (!options.dotsOptions.roundSize) {
      this.element.setAttribute("shape-rendering", "crispEdges");
    }
    
    this.defs = this.window.document.createElementNS("http://www.w3.org/2000/svg", "defs");
    this.element.appendChild(this.defs);
    
    this.dotsClipPath = null;
    this.cornersSquareClipPath = null;
    this.cornersDotClipPath = null;
    this.backgroundClipPath = null;
  }

  getElement() {
    return this.element;
  }

  async drawQR(qr) {
    this.qr = qr;
    const moduleCount = qr.getModuleCount();
    const availableSize = Math.min(this.options.width, this.options.height) - 2 * this.options.margin;
    const actualSize = this.options.shape === "circle" ? availableSize / Math.sqrt(2) : availableSize;
    const dotSize = this.roundSize(actualSize / moduleCount);
    
    this.drawBackground();
    this.drawDots(dotSize, moduleCount);
    this.drawCorners(dotSize, moduleCount);
    
    if (this.options.image) {
      await this.drawImage(dotSize, moduleCount);
    }
  }

  roundSize(size) {
    return this.options.dotsOptions.roundSize ? Math.floor(size) : size;
  }

  drawBackground() {
    const { backgroundOptions } = this.options;
    if (!backgroundOptions || (!backgroundOptions.color && !backgroundOptions.gradient)) return;

    const rect = this.window.document.createElementNS("http://www.w3.org/2000/svg", "rect");
    rect.setAttribute("x", "0");
    rect.setAttribute("y", "0");
    rect.setAttribute("width", String(this.options.width));
    rect.setAttribute("height", String(this.options.height));

    if (backgroundOptions.round) {
      const size = Math.min(this.options.width, this.options.height);
      rect.setAttribute("rx", String(size / 2 * backgroundOptions.round));
    }

    this.createColor({
      options: backgroundOptions.gradient,
      color: backgroundOptions.color,
      element: rect,
      name: `background-color-${this.instanceId}`
    });

    this.element.appendChild(rect);
  }

  drawDots(dotSize, moduleCount) {
    const offsetX = this.roundSize((this.options.width - moduleCount * dotSize) / 2);
    const offsetY = this.roundSize((this.options.height - moduleCount * dotSize) / 2);
    
    this.dotsClipPath = this.window.document.createElementNS("http://www.w3.org/2000/svg", "clipPath");
    this.dotsClipPath.setAttribute("id", `clip-path-dot-color-${this.instanceId}`);
    this.defs.appendChild(this.dotsClipPath);

    const dotFigure = new DotFigure(this.element, this.options.dotsOptions.type, this.window);

    for (let row = 0; row < moduleCount; row++) {
      for (let col = 0; col < moduleCount; col++) {
        if (this.shouldSkipDot(row, col, moduleCount)) continue;
        
        if (this.qr.isDark(row, col)) {
          dotFigure.draw(
            offsetX + col * dotSize,
            offsetY + row * dotSize,
            dotSize,
            (deltaCol, deltaRow) => {
              const newRow = row + deltaRow;
              const newCol = col + deltaCol;
              return newRow >= 0 && newRow < moduleCount && 
                     newCol >= 0 && newCol < moduleCount && 
                     !this.shouldSkipDot(newRow, newCol, moduleCount) &&
                     this.qr.isDark(newRow, newCol);
            }
          );
          
          if (dotFigure.element) {
            this.dotsClipPath.appendChild(dotFigure.element);
          }
        }
      }
    }

    this.createColorRect({
      options: this.options.dotsOptions.gradient,
      color: this.options.dotsOptions.color,
      clipPath: `clip-path-dot-color-${this.instanceId}`,
      name: `dot-color-${this.instanceId}`
    });
  }

  drawCorners(dotSize, moduleCount) {
    const offsetX = this.roundSize((this.options.width - moduleCount * dotSize) / 2);
    const offsetY = this.roundSize((this.options.height - moduleCount * dotSize) / 2);
    const cornerSize = 7 * dotSize;
    const cornerDotSize = 3 * dotSize;

    const corners = [
      [0, 0],
      [moduleCount - 7, 0],
      [0, moduleCount - 7]
    ];

    corners.forEach(([startRow, startCol]) => {
      const x = offsetX + startCol * dotSize;
      const y = offsetY + startRow * dotSize;

      // Desenhar canto quadrado
      if (this.options.cornersSquareOptions && 
          (this.options.cornersSquareOptions.color || this.options.cornersSquareOptions.gradient)) {
        
        this.cornersSquareClipPath = this.window.document.createElementNS("http://www.w3.org/2000/svg", "clipPath");
        this.cornersSquareClipPath.setAttribute("id", `clip-path-corners-square-color-${startRow}-${startCol}-${this.instanceId}`);
        this.defs.appendChild(this.cornersSquareClipPath);

        if (this.options.cornersSquareOptions.type) {
          const cornerSquareFigure = new CornerSquareFigure(this.element, this.options.cornersSquareOptions.type, this.window);
          cornerSquareFigure.draw(x, y, cornerSize);
          if (cornerSquareFigure.element) {
            this.cornersSquareClipPath.appendChild(cornerSquareFigure.element);
          }
        } else {
          // Usar pontos individuais
          this.drawCornerSquareWithDots(x, y, dotSize, this.cornersSquareClipPath);
        }

        this.createColorRect({
          options: this.options.cornersSquareOptions.gradient,
          color: this.options.cornersSquareOptions.color,
          clipPath: `clip-path-corners-square-color-${startRow}-${startCol}-${this.instanceId}`,
          name: `corners-square-color-${startRow}-${startCol}-${this.instanceId}`
        });
      }

      // Desenhar ponto do canto
      if (this.options.cornersDotOptions && 
          (this.options.cornersDotOptions.color || this.options.cornersDotOptions.gradient)) {
        
        this.cornersDotClipPath = this.window.document.createElementNS("http://www.w3.org/2000/svg", "clipPath");
        this.cornersDotClipPath.setAttribute("id", `clip-path-corners-dot-color-${startRow}-${startCol}-${this.instanceId}`);
        this.defs.appendChild(this.cornersDotClipPath);

        if (this.options.cornersDotOptions.type) {
          const cornerDotFigure = new CornerDotFigure(this.element, this.options.cornersDotOptions.type, this.window);
          cornerDotFigure.draw(x + 2 * dotSize, y + 2 * dotSize, cornerDotSize);
          if (cornerDotFigure.element) {
            this.cornersDotClipPath.appendChild(cornerDotFigure.element);
          }
        } else {
          // Usar pontos individuais
          this.drawCornerDotWithDots(x, y, dotSize, this.cornersDotClipPath);
        }

        this.createColorRect({
          options: this.options.cornersDotOptions.gradient,
          color: this.options.cornersDotOptions.color,
          clipPath: `clip-path-corners-dot-color-${startRow}-${startCol}-${this.instanceId}`,
          name: `corners-dot-color-${startRow}-${startCol}-${this.instanceId}`
        });
      }
    });
  }

  drawCornerSquareWithDots(x, y, dotSize, clipPath) {
    const dotFigure = new DotFigure(this.element, this.options.dotsOptions.type, this.window);
    
    for (let row = 0; row < cornerSquarePattern.length; row++) {
      for (let col = 0; col < cornerSquarePattern[row].length; col++) {
        if (cornerSquarePattern[row][col]) {
          dotFigure.draw(
            x + col * dotSize,
            y + row * dotSize,
            dotSize,
            (deltaCol, deltaRow) => {
              const newRow = row + deltaRow;
              const newCol = col + deltaCol;
              return newRow >= 0 && newRow < cornerSquarePattern.length &&
                     newCol >= 0 && newCol < cornerSquarePattern[0].length &&
                     cornerSquarePattern[newRow][newCol];
            }
          );
          
          if (dotFigure.element && clipPath) {
            clipPath.appendChild(dotFigure.element);
          }
        }
      }
    }
  }

  drawCornerDotWithDots(x, y, dotSize, clipPath) {
    const dotFigure = new DotFigure(this.element, this.options.dotsOptions.type, this.window);
    
    for (let row = 0; row < cornerDotPattern.length; row++) {
      for (let col = 0; col < cornerDotPattern[row].length; col++) {
        if (cornerDotPattern[row][col]) {
          dotFigure.draw(
            x + col * dotSize,
            y + row * dotSize,
            dotSize,
            (deltaCol, deltaRow) => {
              const newRow = row + deltaRow;
              const newCol = col + deltaCol;
              return newRow >= 0 && newRow < cornerDotPattern.length &&
                     newCol >= 0 && newCol < cornerDotPattern[0].length &&
                     cornerDotPattern[newRow][newCol];
            }
          );
          
          if (dotFigure.element && clipPath) {
            clipPath.appendChild(dotFigure.element);
          }
        }
      }
    }
  }

  shouldSkipDot(row, col, moduleCount) {
    // Pular cantos (7x7)
    if ((row < 7 && col < 7) || 
        (row < 7 && col >= moduleCount - 7) || 
        (row >= moduleCount - 7 && col < 7)) {
      return true;
    }

    // Pular se deve ocultar pontos de fundo da imagem
    if (this.options.imageOptions && this.options.imageOptions.hideBackgroundDots) {
      const imageSize = this.options.imageOptions.imageSize || 0.4;
      const hiddenDots = Math.floor(imageSize * moduleCount);
      const startHidden = Math.floor((moduleCount - hiddenDots) / 2);
      const endHidden = startHidden + hiddenDots;
      
      if (row >= startHidden && row < endHidden && col >= startHidden && col < endHidden) {
        return true;
      }
    }

    return false;
  }

  async drawImage(dotSize, moduleCount) {
    if (!this.options.image) return;

    const imageSize = this.options.imageOptions.imageSize || 0.4;
    const imageSizeInDots = Math.floor(imageSize * moduleCount);
    const imagePixelSize = imageSizeInDots * dotSize;
    
    const offsetX = this.roundSize((this.options.width - moduleCount * dotSize) / 2);
    const offsetY = this.roundSize((this.options.height - moduleCount * dotSize) / 2);
    
    const imageX = offsetX + this.roundSize((moduleCount * dotSize - imagePixelSize) / 2);
    const imageY = offsetY + this.roundSize((moduleCount * dotSize - imagePixelSize) / 2);

    const imageElement = this.window.document.createElementNS("http://www.w3.org/2000/svg", "image");
    imageElement.setAttribute("href", this.options.image);
    imageElement.setAttribute("x", String(imageX));
    imageElement.setAttribute("y", String(imageY));
    imageElement.setAttribute("width", String(imagePixelSize));
    imageElement.setAttribute("height", String(imagePixelSize));

    this.element.appendChild(imageElement);
  }

  createColor({ options, color, element, name }) {
    if (options) {
      // Criar gradiente
      const gradient = options.type === "radial" 
        ? this.window.document.createElementNS("http://www.w3.org/2000/svg", "radialGradient")
        : this.window.document.createElementNS("http://www.w3.org/2000/svg", "linearGradient");
      
      gradient.setAttribute("id", name);
      gradient.setAttribute("gradientUnits", "userSpaceOnUse");

      if (options.type === "radial") {
        gradient.setAttribute("cx", "50%");
        gradient.setAttribute("cy", "50%");
        gradient.setAttribute("r", "50%");
      } else {
        const rotation = (options.rotation || 0) * 180 / Math.PI;
        gradient.setAttribute("gradientTransform", `rotate(${rotation})`);
      }

      options.colorStops.forEach(({ offset, color }) => {
        const stop = this.window.document.createElementNS("http://www.w3.org/2000/svg", "stop");
        stop.setAttribute("offset", `${offset * 100}%`);
        stop.setAttribute("stop-color", color);
        gradient.appendChild(stop);
      });

      this.defs.appendChild(gradient);
      element.setAttribute("fill", `url(#${name})`);
    } else if (color) {
      element.setAttribute("fill", color);
    }
  }

  createColorRect({ options, color, clipPath, name }) {
    const rect = this.window.document.createElementNS("http://www.w3.org/2000/svg", "rect");
    rect.setAttribute("x", "0");
    rect.setAttribute("y", "0");
    rect.setAttribute("width", String(this.options.width));
    rect.setAttribute("height", String(this.options.height));
    rect.setAttribute("clip-path", `url(#${clipPath})`);

    this.createColor({ options, color, element: rect, name });
    this.element.appendChild(rect);
  }
}

QRStyling.instanceCount = 0;

export { QRStyling };
