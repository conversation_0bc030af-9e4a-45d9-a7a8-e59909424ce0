<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Library Standalone - Validação</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            text-align: center;
        }
        
        h2 {
            color: #555;
            border-bottom: 2px solid #4267b2;
            padding-bottom: 10px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: #f9f9f9;
        }
        
        .test-item h3 {
            margin-top: 0;
            color: #333;
        }
        
        .qr-container {
            display: flex;
            justify-content: center;
            margin: 15px 0;
            min-height: 300px;
            align-items: center;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .test-info {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
        
        .summary {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .summary h3 {
            margin-top: 0;
            color: #1976d2;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        button {
            background: #4267b2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 5px;
        }
        
        button:hover {
            background: #365899;
        }
        
        .log {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>QR Library Standalone - Validação e Testes</h1>
    
    <div class="summary" id="summary">
        <h3>Resumo dos Testes</h3>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="total-tests">0</div>
                <div class="stat-label">Total de Testes</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="passed-tests">0</div>
                <div class="stat-label">Testes Aprovados</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="failed-tests">0</div>
                <div class="stat-label">Testes Falharam</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="success-rate">0%</div>
                <div class="stat-label">Taxa de Sucesso</div>
            </div>
        </div>
        <button onclick="runAllTests()">Executar Todos os Testes</button>
        <button onclick="clearTests()">Limpar Testes</button>
    </div>

    <div class="container">
        <h2>Testes de Funcionalidade Básica</h2>
        <div class="test-grid" id="basic-tests">
            <!-- Testes serão inseridos aqui -->
        </div>
    </div>

    <div class="container">
        <h2>Testes de Estilização</h2>
        <div class="test-grid" id="styling-tests">
            <!-- Testes serão inseridos aqui -->
        </div>
    </div>

    <div class="container">
        <h2>Testes de Compatibilidade</h2>
        <div class="test-grid" id="compatibility-tests">
            <!-- Testes serão inseridos aqui -->
        </div>
    </div>

    <div class="container">
        <h2>Log de Execução</h2>
        <div class="log" id="test-log"></div>
    </div>

    <script type="module">
        import QRCodeStyling from '../index.js';
        
        // Variáveis globais
        let testResults = [];
        let currentTestId = 0;
        
        // Função para log
        function log(message, type = 'info') {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }
        
        // Função para criar item de teste
        function createTestItem(title, description) {
            const testId = `test-${currentTestId++}`;
            const testItem = document.createElement('div');
            testItem.className = 'test-item';
            testItem.id = testId;
            testItem.innerHTML = `
                <h3>${title}</h3>
                <div class="status" id="${testId}-status">Aguardando...</div>
                <div class="qr-container" id="${testId}-qr"></div>
                <div class="test-info">${description}</div>
            `;
            return { testItem, testId };
        }
        
        // Função para executar teste
        async function executeTest(testId, testFunction, timeout = 5000) {
            const statusElement = document.getElementById(`${testId}-status`);
            const qrContainer = document.getElementById(`${testId}-qr`);
            
            try {
                statusElement.textContent = 'Executando...';
                statusElement.className = 'status warning';
                
                const result = await Promise.race([
                    testFunction(qrContainer),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), timeout))
                ]);
                
                statusElement.textContent = 'Sucesso';
                statusElement.className = 'status success';
                testResults.push({ testId, success: true, error: null });
                log(`Teste ${testId} passou`);
                
                return true;
            } catch (error) {
                statusElement.textContent = `Erro: ${error.message}`;
                statusElement.className = 'status error';
                testResults.push({ testId, success: false, error: error.message });
                log(`Teste ${testId} falhou: ${error.message}`, 'error');
                
                return false;
            }
        }
        
        // Testes básicos
        const basicTests = [
            {
                title: 'QR Code Básico',
                description: 'Teste de criação de QR Code simples',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        data: 'Teste básico'
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                    if (!container.querySelector('svg') && !container.querySelector('canvas')) {
                        throw new Error('QR Code não foi renderizado');
                    }
                }
            },
            {
                title: 'QR Code com URL',
                description: 'Teste com URL longa',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        data: 'https://www.exemplo.com/pagina-muito-longa-com-parametros?param1=valor1&param2=valor2'
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                    if (!container.querySelector('svg') && !container.querySelector('canvas')) {
                        throw new Error('QR Code não foi renderizado');
                    }
                }
            },
            {
                title: 'QR Code Canvas',
                description: 'Teste de renderização em Canvas',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        type: 'canvas',
                        data: 'Teste Canvas'
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 200));
                    if (!container.querySelector('canvas')) {
                        throw new Error('Canvas não foi criado');
                    }
                }
            },
            {
                title: 'QR Code SVG',
                description: 'Teste de renderização em SVG',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        type: 'svg',
                        data: 'Teste SVG'
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                    if (!container.querySelector('svg')) {
                        throw new Error('SVG não foi criado');
                    }
                }
            },
            {
                title: 'Update de Dados',
                description: 'Teste de atualização de dados',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        data: 'Dados iniciais'
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    qr.update({ data: 'Dados atualizados' });
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    if (!container.querySelector('svg') && !container.querySelector('canvas')) {
                        throw new Error('QR Code não foi atualizado');
                    }
                }
            }
        ];
        
        // Testes de estilização
        const stylingTests = [
            {
                title: 'Pontos Arredondados',
                description: 'Teste de pontos com bordas arredondadas',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        data: 'Pontos arredondados',
                        dotsOptions: {
                            type: 'rounded',
                            color: '#4267b2'
                        }
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            },
            {
                title: 'Gradiente Linear',
                description: 'Teste de gradiente linear nos pontos',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        data: 'Gradiente linear',
                        dotsOptions: {
                            type: 'rounded',
                            gradient: {
                                type: 'linear',
                                rotation: Math.PI / 4,
                                colorStops: [
                                    { offset: 0, color: '#ff6b6b' },
                                    { offset: 1, color: '#4ecdc4' }
                                ]
                            }
                        }
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            },
            {
                title: 'Gradiente Radial',
                description: 'Teste de gradiente radial nos pontos',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        data: 'Gradiente radial',
                        dotsOptions: {
                            type: 'dots',
                            gradient: {
                                type: 'radial',
                                colorStops: [
                                    { offset: 0, color: '#ffffff' },
                                    { offset: 1, color: '#000000' }
                                ]
                            }
                        }
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            },
            {
                title: 'Cantos Personalizados',
                description: 'Teste de cantos quadrados e pontos personalizados',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        data: 'Cantos personalizados',
                        dotsOptions: {
                            type: 'classy-rounded',
                            color: '#4267b2'
                        },
                        cornersSquareOptions: {
                            type: 'extra-rounded',
                            color: '#ff6b6b'
                        },
                        cornersDotOptions: {
                            type: 'dot',
                            color: '#4ecdc4'
                        }
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            },
            {
                title: 'Fundo Personalizado',
                description: 'Teste de cor de fundo personalizada',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        data: 'Fundo personalizado',
                        dotsOptions: {
                            type: 'rounded',
                            color: '#ffffff'
                        },
                        backgroundOptions: {
                            color: '#2c3e50',
                            round: 0.1
                        }
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }
        ];
        
        // Testes de compatibilidade
        const compatibilityTests = [
            {
                title: 'Dados Vazios',
                description: 'Teste com dados vazios (deve usar fallback)',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        data: ''
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                    // Deve criar QR code mesmo com dados vazios
                }
            },
            {
                title: 'Dados Muito Longos',
                description: 'Teste com dados muito longos',
                test: async (container) => {
                    const longData = 'A'.repeat(1000);
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        data: longData,
                        qrOptions: {
                            errorCorrectionLevel: 'L'
                        }
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
            },
            {
                title: 'Caracteres Especiais',
                description: 'Teste com caracteres especiais e emojis',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 200,
                        height: 200,
                        data: 'Teste com acentos: ção, ã, é, ü 🚀 ❤️ 🎉'
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            },
            {
                title: 'Tamanhos Extremos',
                description: 'Teste com tamanhos muito pequenos e grandes',
                test: async (container) => {
                    const qr = new QRCodeStyling({
                        width: 50,
                        height: 50,
                        data: 'Pequeno'
                    });
                    qr.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    // Limpar e testar tamanho grande
                    container.innerHTML = '';
                    const qrLarge = new QRCodeStyling({
                        width: 400,
                        height: 400,
                        data: 'Grande'
                    });
                    qrLarge.append(container);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }
        ];
        
        // Função para executar todos os testes
        window.runAllTests = async function() {
            log('Iniciando execução de todos os testes...');
            testResults = [];
            
            // Executar testes básicos
            const basicContainer = document.getElementById('basic-tests');
            basicContainer.innerHTML = '';
            for (const test of basicTests) {
                const { testItem, testId } = createTestItem(test.title, test.description);
                basicContainer.appendChild(testItem);
                await executeTest(testId, test.test);
            }
            
            // Executar testes de estilização
            const stylingContainer = document.getElementById('styling-tests');
            stylingContainer.innerHTML = '';
            for (const test of stylingTests) {
                const { testItem, testId } = createTestItem(test.title, test.description);
                stylingContainer.appendChild(testItem);
                await executeTest(testId, test.test);
            }
            
            // Executar testes de compatibilidade
            const compatibilityContainer = document.getElementById('compatibility-tests');
            compatibilityContainer.innerHTML = '';
            for (const test of compatibilityTests) {
                const { testItem, testId } = createTestItem(test.title, test.description);
                compatibilityContainer.appendChild(testItem);
                await executeTest(testId, test.test);
            }
            
            // Atualizar resumo
            updateSummary();
            log('Todos os testes foram executados');
        };
        
        // Função para limpar testes
        window.clearTests = function() {
            document.getElementById('basic-tests').innerHTML = '';
            document.getElementById('styling-tests').innerHTML = '';
            document.getElementById('compatibility-tests').innerHTML = '';
            document.getElementById('test-log').textContent = '';
            testResults = [];
            currentTestId = 0;
            updateSummary();
            log('Testes limpos');
        };
        
        // Função para atualizar resumo
        function updateSummary() {
            const total = testResults.length;
            const passed = testResults.filter(r => r.success).length;
            const failed = total - passed;
            const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;
            
            document.getElementById('total-tests').textContent = total;
            document.getElementById('passed-tests').textContent = passed;
            document.getElementById('failed-tests').textContent = failed;
            document.getElementById('success-rate').textContent = `${successRate}%`;
        }
        
        // Inicializar
        log('Sistema de validação carregado');
        updateSummary();
    </script>
</body>
</html>
