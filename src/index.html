<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Generate branded QR code online, you can add image and color to your QR code. Use the QR code styling library in your site to generate QR codes dynamically." />
    <meta name="keywords" content="qr, qr code, qrcode, style, styling, brand, branding, color, image, create, creating, generate, generator, generating" />
    <link rel="preconnect" href="https://www.google-analytics.com">
    <link rel="shortcut icon" type="image/x-icon" href="<%=require('./assets/qr_trans.png')%>" />
    <title>QR Code Styling</title>
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-149838823-1"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'UA-149838823-1');
    </script>
</head>
<body>
    <header class="container qr-header">
        <div class="row row--qr-header">
            <div class="col qr-logo">
                <img class="qr-logo__img" src="<%=require('./assets/logo1.png')%>" alt="QR code styling logo" />
            </div>
            <a target="_blank" rel="noopener" class="col white" href="https://www.npmjs.com/package/qr-code-styling">npm v1.8.3</a>
            <a target="_blank" rel="noopener" class="col white" href="https://github.com/kozakdenys/qr-code-styling">GitHub</a>
        </div>
    </header>
    <main>
        <section class="container qr-description" id="qr-description">
            <div class="col">
                <h1 class="qr-description__lib-name">QR Code Styling</h1>
                <p>An open source JS library</p>
                <p>For generating styled QR codes</p>
            </div>
        </section>
        <section class="container">
            <div class="row row--body">
                <form class="col qr-form" id="form">
                    <button type="button" class="accordion accordion--open">Main Options</button>
                    <div class="panel panel--open">
                        <label for="form-data">Data</label>
                        <textarea node="data" node-change-event="oninput" id="form-data" type="text" value="https://qr-code-styling.com">https://qr-code-styling.com</textarea>
                        <label for="form-image-file">Image File</label>
                        <div class="buttons-container">
                            <input node="image" node-data-field="files" id="form-image-file" type="file" />
                            <button type="button" id="button-cancel">Cancel</button>
                        </div>
                        <label for="form-width">Width</label>
                        <div>
                            <input node="width" id="form-width" type="number" min="100" max="10000" value="300"/>
                        </div>
                        <label for="form-height">Height</label>
                        <div>
                            <input node="height" id="form-height" type="number" min="100" max="10000" value="300"/>
                        </div>
                        <label for="form-height">Margin</label>
                        <div>
                            <input node="margin" id="form-margin" type="number" min="0" max="10000" value="0"/>
                        </div>
                    </div>
                    <button type="button" class="accordion">Dots Options</button>
                    <div class="panel">
                        <label for="form-dots-type">Dots Style</label>
                        <div>
                            <select node="dotsOptions.type" id="form-dots-type">
                                <option value="square">Square</option>
                                <option value="dots">Dots</option>
                                <option value="rounded">Rounded</option>
                                <option value="extra-rounded" selected>Extra rounded</option>
                                <option value="classy">Classy</option>
                                <option value="classy-rounded">Classy rounded</option>
                            </select>
                        </div>
                        <label>Color Type</label>
                        <div class="space-between-container">
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="dotsOptionsHelper.colorType.single" id="form-dots-color-type-single" type="radio" name="dots-color-type" checked/>
                                <label for="form-dots-color-type-single">Single color</label>
                            </div>
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="dotsOptionsHelper.colorType.gradient" id="form-dots-color-type-gradient" type="radio" name="dots-color-type"/>
                                <label for="form-dots-color-type-gradient">Color gradient</label>
                            </div>
                        </div>
                        <label class="dotsOptionsHelper.colorType.single" for="form-dots-color">Dots Color</label>
                        <div class="dotsOptionsHelper.colorType.single">
                            <input node="dotsOptions.color" id="form-dots-color" type="color" value="#6a1a4c"/>
                        </div>
                        <label class="dotsOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">Gradient Type</label>
                        <div class="dotsOptionsHelper.colorType.gradient space-between-container" style="visibility: hidden; height: 0">
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="dotsOptionsHelper.gradient.linear" id="form-dots-gradient-type-linear" type="radio" name="dots-gradient-type" checked/>
                                <label for="form-dots-gradient-type-linear">Linear</label>
                            </div>
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="dotsOptionsHelper.gradient.radial" id="form-dots-gradient-type-radial" type="radio" name="dots-gradient-type"/>
                                <label for="form-dots-gradient-type-radial">Radial</label>
                            </div>
                        </div>
                        <label class="dotsOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">Dots Gradient</label>
                        <div class="dotsOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">
                            <input node="dotsOptionsHelper.gradient.color1" type="color" value="#6a1a4c"/>
                            <input node="dotsOptionsHelper.gradient.color2" type="color" value="#6a1a4c"/>
                        </div>
                        <label class="dotsOptionsHelper.colorType.gradient" for="form-dots-gradient-rotation" style="visibility: hidden; height: 0">Rotation</label>
                        <div class="dotsOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">
                            <input node="dotsOptionsHelper.gradient.rotation" id="form-dots-gradient-rotation" type="number" min="0" max="360" value="0"/>
                        </div>
                    </div>
                    <button type="button" class="accordion">Corners Square Options</button>
                    <div class="panel">
                        <label for="form-corners-square-type">Corners Square Style</label>
                        <div>
                            <select node="cornersSquareOptions.type" id="form-corners-square-type">
                                <option value="">None</option>
                                <option value="square">Square</option>
                                <option value="dot">Dot</option>
                                <option value="extra-rounded" selected>Extra rounded</option>
                            </select>
                        </div>
                        <label>Color Type</label>
                        <div class="space-between-container">
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="cornersSquareOptionsHelper.colorType.single" id="form-corners-square-color-type-single" type="radio" name="corners-square-color-type" checked/>
                                <label for="form-corners-square-color-type-single">Single color</label>
                            </div>
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="cornersSquareOptionsHelper.colorType.gradient" id="form-corners-square-color-type-gradient" type="radio" name="corners-square-color-type"/>
                                <label for="form-corners-square-color-type-gradient">Color gradient</label>
                            </div>
                        </div>
                        <label class="cornersSquareOptionsHelper.colorType.single" for="form-corners-square-color">Corners Square Color</label>
                        <div class="cornersSquareOptionsHelper.colorType.single buttons-container">
                            <input node="cornersSquareOptions.color" id="form-corners-square-color" type="color" value="#000000"/>
                            <button type="button" id="button-clear-corners-square-color">Clear</button>
                        </div>
                        <label class="cornersSquareOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">Gradient Type</label>
                        <div class="cornersSquareOptionsHelper.colorType.gradient space-between-container" style="visibility: hidden; height: 0">
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="cornersSquareOptionsHelper.gradient.linear" id="form-corners-square-gradient-type-linear" type="radio" name="corners-square-gradient-type" checked/>
                                <label for="form-corners-square-gradient-type-linear">Linear</label>
                            </div>
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="cornersSquareOptionsHelper.gradient.radial" id="form-corners-square-gradient-type-radial" type="radio" name="corners-square-gradient-type"/>
                                <label for="form-corners-square-gradient-type-radial">Radial</label>
                            </div>
                        </div>
                        <label class="cornersSquareOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">Dots Gradient</label>
                        <div class="cornersSquareOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">
                            <input node="cornersSquareOptionsHelper.gradient.color1" type="color" value="#000000"/>
                            <input node="cornersSquareOptionsHelper.gradient.color2" type="color" value="#000000"/>
                        </div>
                        <label class="cornersSquareOptionsHelper.colorType.gradient" for="form-corners-square-gradient-rotation" style="visibility: hidden; height: 0">Rotation</label>
                        <div class="cornersSquareOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">
                            <input node="cornersSquareOptionsHelper.gradient.rotation" id="form-corners-square-gradient-rotation" type="number" min="0" max="360" value="0"/>
                        </div>
                    </div>
                    <button type="button" class="accordion">Corners Dot Options</button>
                    <div class="panel">
                        <label for="form-corners-dot-type">Corners Dot Style</label>
                        <div>
                            <select node="cornersDotOptions.type" id="form-corners-dot-type">
                                <option value="" selected>None</option>
                                <option value="square">Square</option>
                                <option value="dot">Dot</option>
                            </select>
                        </div>
                        <label>Color Type</label>
                        <div class="space-between-container">
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="cornersDotOptionsHelper.colorType.single" id="form-corners-dot-color-type-single" type="radio" name="corners-dot-color-type" checked/>
                                <label for="form-corners-dot-color-type-single">Single color</label>
                            </div>
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="cornersDotOptionsHelper.colorType.gradient" id="form-corners-dot-color-type-gradient" type="radio" name="corners-dot-color-type"/>
                                <label for="form-corners-dot-color-type-gradient">Color gradient</label>
                            </div>
                        </div>
                        <label class="cornersDotOptionsHelper.colorType.single" for="form-corners-dot-color">Corners Dot Color</label>
                        <div class="cornersDotOptionsHelper.colorType.single buttons-container">
                            <input node="cornersDotOptions.color" id="form-corners-dot-color" type="color" value="#000000"/>
                            <button type="button" id="button-clear-corners-dot-color">Clear</button>
                        </div>
                        <label class="cornersDotOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">Gradient Type</label>
                        <div class="cornersDotOptionsHelper.colorType.gradient space-between-container" style="visibility: hidden; height: 0">
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="cornersDotOptionsHelper.gradient.linear" id="form-corners-dot-gradient-type-linear" type="radio" name="corners-dot-gradient-type" checked/>
                                <label for="form-corners-dot-gradient-type-linear">Linear</label>
                            </div>
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="cornersDotOptionsHelper.gradient.radial" id="form-corners-dot-gradient-type-radial" type="radio" name="corners-dot-gradient-type"/>
                                <label for="form-corners-dot-gradient-type-radial">Radial</label>
                            </div>
                        </div>
                        <label class="cornersDotOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">Dots Gradient</label>
                        <div class="cornersDotOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">
                            <input node="cornersDotOptionsHelper.gradient.color1" type="color" value="#000000"/>
                            <input node="cornersDotOptionsHelper.gradient.color2" type="color" value="#000000"/>
                        </div>
                        <label class="cornersDotOptionsHelper.colorType.gradient" for="form-corners-dot-gradient-rotation" style="visibility: hidden; height: 0">Rotation</label>
                        <div class="cornersDotOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">
                            <input node="cornersDotOptionsHelper.gradient.rotation" id="form-corners-dot-gradient-rotation" type="number" min="0" max="360" value="0"/>
                        </div>
                    </div>
                    <button type="button" class="accordion">Background Options</button>
                    <div class="panel">
                        <label>Color Type</label>
                        <div class="space-between-container">
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="backgroundOptionsHelper.colorType.single" id="form-background-color-type-single" type="radio" name="background-color-type" checked/>
                                <label for="form-background-color-type-single">Single color</label>
                            </div>
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="backgroundOptionsHelper.colorType.gradient" id="form-background-color-type-gradient" type="radio" name="background-color-type"/>
                                <label for="form-background-color-type-gradient">Color gradient</label>
                            </div>
                        </div>
                        <label class="backgroundOptionsHelper.colorType.single" for="form-background-color">Background Color</label>
                        <div class="backgroundOptionsHelper.colorType.single">
                            <input node="backgroundOptions.color" id="form-background-color" type="color" value="#ffffff"/>
                        </div>
                        <label class="backgroundOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">Gradient Type</label>
                        <div class="backgroundOptionsHelper.colorType.gradient space-between-container" style="visibility: hidden; height: 0">
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="backgroundOptionsHelper.gradient.linear" id="form-background-gradient-type-linear" type="radio" name="background-gradient-type" checked/>
                                <label for="form-background-gradient-type-linear">Linear</label>
                            </div>
                            <div style="flex-grow: 1">
                                <input node-data-field="checked" node="backgroundOptionsHelper.gradient.radial" id="form-background-gradient-type-radial" type="radio" name="background-gradient-type"/>
                                <label for="form-background-gradient-type-radial">Radial</label>
                            </div>
                        </div>
                        <label class="backgroundOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">Background Gradient</label>
                        <div class="backgroundOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">
                            <input node="backgroundOptionsHelper.gradient.color1" type="color" value="#ffffff"/>
                            <input node="backgroundOptionsHelper.gradient.color2" type="color" value="#ffffff"/>
                        </div>
                        <label class="backgroundOptionsHelper.colorType.gradient" for="form-background-gradient-rotation" style="visibility: hidden; height: 0">Rotation</label>
                        <div class="backgroundOptionsHelper.colorType.gradient" style="visibility: hidden; height: 0">
                            <input node="backgroundOptionsHelper.gradient.rotation" id="form-background-gradient-rotation" type="number" min="0" max="360" value="0"/>
                        </div>
                    </div>
                    <button type="button" class="accordion">Image Options</button>
                    <div class="panel">
                        <label for="form-hide-background-dots">Hide Background Dots</label>
                        <div>
                            <input node="imageOptions.hideBackgroundDots" node-data-field="checked" id="form-hide-background-dots" type="checkbox" checked/>
                        </div>
                        <label for="form-image-size">Image Size</label>
                        <div>
                            <input node="imageOptions.imageSize" id="form-image-size" type="number" min="0" max="1" step="0.1" value="0.4"/>
                        </div>
                        <label for="form-image-margin">Margin</label>
                        <div>
                            <input node="imageOptions.margin" id="form-image-margin" type="number" min="0" max="10000" value="0"/>
                        </div>
                    </div>
                    <button type="button" class="accordion">QR Options</button>
                    <div class="panel">
                        <label for="form-qr-type-number">Type Number</label>
                        <div>
                            <input node="qrOptions.typeNumber" id="form-qr-type-number" type="number" min="0" max="40" value="0"/>
                        </div>
                        <label for="form-qr-mode">Mode</label>
                        <div>
                            <select node="qrOptions.mode" id="form-qr-mode">
                                <option value="Numeric">Numeric</option>
                                <option value="Alphanumeric">Alphanumeric</option>
                                <option value="Byte" selected>Byte</option>
                                <option value="Kanji">Kanji</option>
                            </select>
                        </div>
                        <label for="form-qr-error-correction-level">Error Correction Level</label>
                        <div>
                            <select node="qrOptions.errorCorrectionLevel" id="form-qr-error-correction-level">
                                <option value="L">L</option>
                                <option value="M">M</option>
                                <option value="Q" selected>Q</option>
                                <option value="H">H</option>
                            </select>
                        </div>
                    </div>
                    <div class="options-export-group">
                        <a class="button" id="export-options">Export Options as JSON</a>
                    </div>
                </form>
                <div class="col qr-code-container">
                    <div class="qr-code" id="qr-code-generated"></div>
                    <div class="qr-download-group">
                        <button id="qr-download">Download</button>
                        <label class="hide" for="qr-extension">Extension</label>
                        <select id="qr-extension">
                            <option value="png" selected>PNG</option>
                            <option value="jpeg">JPEG</option>
                            <option value="svg">SVG</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col">
                    <p>If you have any questions or issues please contact me via <a class="black" rel="noopener" href="mailto:<EMAIL>">email</a> or <a class="black" target="_blank" rel="noopener" href="https://github.com/kozakdenys/qr-code-styling">GitHub Issues</a>.
                </div>
            </div>
        </section>
    </main>
    <footer class="container qr-footer">
        <div class="col">
            © 2024 <a target="_blank" rel="noopener" class="white" href="https://linktr.ee/denys.kozak">Denys Kozak</a>
        </div>
    </footer>
</body>
</html>
